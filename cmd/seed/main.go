package main

import (
	"context"
	"flag"
	"fmt"
	"os"
	"strings"
	"time"

	"wnapi/internal/pkg/config/viperconfig"
	"wnapi/internal/pkg/logger"
	"wnapi/internal/pkg/seed"
	authSeeds "wnapi/modules/auth/seeds"

	_ "github.com/go-sql-driver/mysql"
	"github.com/jmoiron/sqlx"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
)

var (
	envFlag     = flag.String("env", "dev", "Environment (dev, staging, prod)")
	moduleFlag  = flag.String("module", "", "Module name to seed")
	seederFlag  = flag.String("seeder", "", "Specific seeder to run")
	listFlag    = flag.Bool("list", false, "List available seeds")
	dryRunFlag  = flag.Bool("dry-run", false, "Dry run mode")
	verboseFlag = flag.Bool("verbose", false, "Verbose logging")
	configFlag  = flag.String("config", "@.env", "Config file path")
)

func main() {
	flag.Parse()

	// Load application config
	fmt.Printf("Loading config from: %s\n", *configFlag)
	appConfig, err := viperconfig.NewConfigLoader().Load(*configFlag)
	if err != nil {
		fmt.Printf("Failed to load config: %v\n", err)
		os.Exit(1)
	}
	fmt.Printf("Config loaded successfully\n")

	// Initialize logger
	logLevelStr := "info"
	if *verboseFlag {
		logLevelStr = "debug"
	}
	
	// Parse log level
	var logLevel logger.Level
	switch strings.ToLower(logLevelStr) {
	case "debug":
		logLevel = logger.LevelDebug
	case "info":
		logLevel = logger.LevelInfo
	case "warn", "warning":
		logLevel = logger.LevelWarn
	case "error":
		logLevel = logger.LevelError
	case "fatal":
		logLevel = logger.LevelFatal
	default:
		logLevel = logger.LevelDebug
	}
	
	fmt.Printf("Initializing logger with level: %s\n", logLevelStr)
	log := logger.NewConsoleLogger("wnapi-seed", logLevel)
	fmt.Printf("Logger initialized successfully\n")

	// Initialize database connection
	fmt.Printf("Building database connection string...\n")
	dsn := fmt.Sprintf("%s:%s@tcp(%s:%s)/%s?parseTime=true",
		appConfig.GetString("db_username"),
		appConfig.GetString("db_password"),
		appConfig.GetString("db_host"),
		appConfig.GetString("db_port"),
		appConfig.GetString("db_database"),
	)
	fmt.Printf("DSN: %s\n", dsn)

	fmt.Printf("Attempting to connect to database...\n")
	log.Info("Attempting to connect to database", "dsn", dsn)
	sqlxDB, err := sqlx.Connect("mysql", dsn)
	if err != nil {
		fmt.Printf("Database connection failed: %v\n", err)
		log.Error("Failed to connect to database", "error", err, "dsn", dsn)
		os.Exit(1)
	}
	defer sqlxDB.Close()
	fmt.Printf("Database connection successful\n")
	log.Info("Database connection successful")

	// Configure connection pool
	fmt.Printf("Configuring connection pool...\n")
	sqlxDB.SetMaxOpenConns(25)
	sqlxDB.SetMaxIdleConns(5)
	sqlxDB.SetConnMaxLifetime(5 * time.Minute)

	// Create GORM DB from sqlx DB
	fmt.Printf("Creating GORM DB...\n")
	gormDB, err := gorm.Open(mysql.New(mysql.Config{
		Conn: sqlxDB.DB,
	}), &gorm.Config{})
	if err != nil {
		fmt.Printf("Failed to initialize GORM: %v\n", err)
		log.Error("Failed to initialize GORM", "error", err)
		os.Exit(1)
	}
	fmt.Printf("GORM initialized successfully\n")

	// Initialize seed config
	fmt.Printf("Initializing seed config...\n")
	config := seed.LoadConfig()
	config.Environment = *envFlag
	config.DryRun = *dryRunFlag
	config.Verbose = *verboseFlag

	if err := config.Validate(); err != nil {
		fmt.Printf("Invalid seed config: %v\n", err)
		log.Error("Invalid config", "error", err)
		os.Exit(1)
	}
	fmt.Printf("Seed config initialized successfully\n")

	fmt.Printf("Starting seed command...\n")
	log.Info("Seed command started",
		"environment", config.Environment,
		"dry_run", config.DryRun)

	// Initialize seed registry
	fmt.Printf("Initializing seed registry...\n")
	if err := initializeSeedRegistry(gormDB, log, config); err != nil {
		fmt.Printf("Failed to initialize seed registry: %v\n", err)
		log.Error("Failed to initialize seed registry", "error", err)
		os.Exit(1)
	}
	fmt.Printf("Seed registry initialized successfully\n")

	// Handle commands
	fmt.Printf("Handling commands...\n")
	if *listFlag {
		fmt.Printf("Listing seeds...\n")
		listSeeds(log)
		return
	}

	if *moduleFlag != "" {
		fmt.Printf("Module flag set: %s\n", *moduleFlag)
		if *seederFlag != "" {
			fmt.Printf("Seeding specific: %s/%s\n", *moduleFlag, *seederFlag)
			seedSpecific(gormDB, log, config, *moduleFlag, *seederFlag)
		} else {
			fmt.Printf("Seeding module: %s\n", *moduleFlag)
			seedModule(gormDB, log, config, *moduleFlag)
		}
		return
	}

	// Default: seed all modules
	fmt.Printf("Seeding all modules...\n")
	seedAll(gormDB, log, config)
}

func listSeeds(log logger.Logger) {
	log.Info("Listing available seeds...")

	seeds := seed.ListAvailableSeeds()
	if len(seeds) == 0 {
		log.Info("No seeds registered")
		return
	}

	for moduleName, seeders := range seeds {
		log.Info("Module", "name", moduleName)
		for _, seederName := range seeders {
			log.Info("  - Seeder", "name", seederName)
		}
	}
}

// initializeSeedRegistry initializes the seed registry with all module seeds
func initializeSeedRegistry(gormDB *gorm.DB, log logger.Logger, config *seed.Config) error {
	// Register auth seeds
	if err := authSeeds.RegisterAuthSeed(nil, gormDB, log, config); err != nil {
		return fmt.Errorf("failed to register auth seeds: %w", err)
	}

	// NOTE: Other module seeds can be registered here when needed
	// Example: tenantSeeds.RegisterTenantSeed(nil, gormDB, log, config)

	return nil
}

func seedAll(gormDB *gorm.DB, log logger.Logger, config *seed.Config) {
	log.Info("Seeding all modules...")

	runner := seed.NewRunner(gormDB, log, config)
	ctx := context.Background()

	if err := runner.SeedAllModules(ctx); err != nil {
		log.Error("Failed to seed all modules", "error", err)
		os.Exit(1)
	}

	log.Info("All modules seeded successfully")
}

func seedModule(gormDB *gorm.DB, log logger.Logger, config *seed.Config, moduleName string) {
	fmt.Printf("Starting seedModule function for: %s\n", moduleName)
	log.Info("Seeding module", "module", moduleName)

	fmt.Printf("Creating runner...\n")
	runner := seed.NewRunner(gormDB, log, config)
	ctx := context.Background()

	fmt.Printf("Calling runner.SeedModule...\n")
	if err := runner.SeedModule(ctx, moduleName); err != nil {
		fmt.Printf("SeedModule failed: %v\n", err)
		log.Error("Failed to seed module", "module", moduleName, "error", err)
		os.Exit(1)
	}

	fmt.Printf("Module seeded successfully: %s\n", moduleName)
	log.Info("Module seeded successfully", "module", moduleName)
}

func seedSpecific(gormDB *gorm.DB, log logger.Logger, config *seed.Config, moduleName, seederName string) {
	log.Info("Seeding specific", "module", moduleName, "seeder", seederName)

	runner := seed.NewRunner(gormDB, log, config)
	ctx := context.Background()

	if err := runner.SeedSpecific(ctx, moduleName, seederName); err != nil {
		log.Error("Failed to seed specific", "module", moduleName, "seeder", seederName, "error", err)
		os.Exit(1)
	}

	log.Info("Seeder completed successfully", "module", moduleName, "seeder", seederName)
}
