package commands

import (
	"context"
	"fmt"

	"wnapi/internal/core"
	"wnapi/internal/pkg/console"
	"wnapi/internal/pkg/seed"
	authSeeds "wnapi/modules/auth/seeds"

	"github.com/spf13/cobra"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
)

// newSeedCommand creates the seed command
func newSeedCommand(app *core.AppBootstrap) *cobra.Command {
	cmd := &cobra.Command{
		Use:   "seed",
		Short: "Database seeding operations",
		Long:  `Manage database seeding operations for modules`,
	}

	// Add subcommands
	cmd.AddCommand(newSeedListCommand(app))
	cmd.AddCommand(newSeedAllCommand(app))
	cmd.AddCommand(newSeedModuleCommand(app))
	cmd.AddCommand(newSeedSpecificCommand(app))
	cmd.AddCommand(newSeedRollbackCommand(app))

	return cmd
}

// newSeedListCommand creates the seed list command
func newSeedListCommand(app *core.AppBootstrap) *cobra.Command {
	return &cobra.Command{
		Use:   "list",
		Short: "List all available seeds",
		RunE: func(cmd *cobra.Command, args []string) error {
			// Initialize seed registry
			if err := initializeSeedRegistry(app); err != nil {
				return fmt.Errorf("failed to initialize seed registry: %w", err)
			}

			seeds := seed.ListAvailableSeeds()
			if len(seeds) == 0 {
				console.Info("No seeds registered")
				return nil
			}

			console.Info("Available seeds:")
			for moduleName, seeders := range seeds {
				console.Info(fmt.Sprintf("Module: %s", moduleName))
				for _, seederName := range seeders {
					console.Info(fmt.Sprintf("  - %s", seederName))
				}
			}

			return nil
		},
	}
}

// newSeedAllCommand creates the seed all command
func newSeedAllCommand(app *core.AppBootstrap) *cobra.Command {
	var (
		environment string
		dryRun      bool
		verbose     bool
	)

	cmd := &cobra.Command{
		Use:   "all",
		Short: "Run all module seeds",
		RunE: func(cmd *cobra.Command, args []string) error {
			config := seed.LoadConfig()
			config.Environment = environment
			config.DryRun = dryRun
			config.Verbose = verbose

			if err := config.Validate(); err != nil {
				return fmt.Errorf("invalid config: %w", err)
			}

			// Initialize seed registry
			if err := initializeSeedRegistry(app); err != nil {
				return fmt.Errorf("failed to initialize seed registry: %w", err)
			}

			// Create database connection
			gormDB, err := createGormDB(app)
			if err != nil {
				return fmt.Errorf("failed to create database connection: %w", err)
			}

			// Create runner
			runner := seed.NewRunner(gormDB, app.GetLogger(), config)

			// Run all modules
			ctx := context.Background()
			if err := runner.SeedAllModules(ctx); err != nil {
				return fmt.Errorf("failed to seed all modules: %w", err)
			}

			console.Success("All modules seeded successfully")
			return nil
		},
	}

	cmd.Flags().StringVarP(&environment, "env", "e", "dev", "Environment (dev, staging, prod)")
	cmd.Flags().BoolVar(&dryRun, "dry-run", false, "Dry run mode")
	cmd.Flags().BoolVarP(&verbose, "verbose", "v", false, "Verbose logging")

	return cmd
}

// newSeedModuleCommand creates the seed module command
func newSeedModuleCommand(app *core.AppBootstrap) *cobra.Command {
	var (
		environment string
		dryRun      bool
		verbose     bool
	)

	cmd := &cobra.Command{
		Use:   "module <module_name>",
		Short: "Run seeds for a specific module",
		Args:  cobra.ExactArgs(1),
		RunE: func(cmd *cobra.Command, args []string) error {
			moduleName := args[0]

			config := seed.LoadConfig()
			config.Environment = environment
			config.DryRun = dryRun
			config.Verbose = verbose

			if err := config.Validate(); err != nil {
				return fmt.Errorf("invalid config: %w", err)
			}

			// Initialize seed registry
			if err := initializeSeedRegistry(app); err != nil {
				return fmt.Errorf("failed to initialize seed registry: %w", err)
			}

			// Create database connection
			gormDB, err := createGormDB(app)
			if err != nil {
				return fmt.Errorf("failed to create database connection: %w", err)
			}

			// Create runner
			runner := seed.NewRunner(gormDB, app.GetLogger(), config)

			// Run module
			ctx := context.Background()
			if err := runner.SeedModule(ctx, moduleName); err != nil {
				return fmt.Errorf("failed to seed module %s: %w", moduleName, err)
			}

			console.Success(fmt.Sprintf("Module '%s' seeded successfully", moduleName))
			return nil
		},
	}

	cmd.Flags().StringVarP(&environment, "env", "e", "dev", "Environment (dev, staging, prod)")
	cmd.Flags().BoolVar(&dryRun, "dry-run", false, "Dry run mode")
	cmd.Flags().BoolVarP(&verbose, "verbose", "v", false, "Verbose logging")

	return cmd
}

// newSeedSpecificCommand creates the seed specific command
func newSeedSpecificCommand(app *core.AppBootstrap) *cobra.Command {
	var (
		environment string
		dryRun      bool
		verbose     bool
	)

	cmd := &cobra.Command{
		Use:   "specific <module_name> <seeder_name>",
		Short: "Run a specific seeder",
		Args:  cobra.ExactArgs(2),
		RunE: func(cmd *cobra.Command, args []string) error {
			moduleName := args[0]
			seederName := args[1]

			config := seed.LoadConfig()
			config.Environment = environment
			config.DryRun = dryRun
			config.Verbose = verbose

			if err := config.Validate(); err != nil {
				return fmt.Errorf("invalid config: %w", err)
			}

			// Initialize seed registry
			if err := initializeSeedRegistry(app); err != nil {
				return fmt.Errorf("failed to initialize seed registry: %w", err)
			}

			// Create database connection
			gormDB, err := createGormDB(app)
			if err != nil {
				return fmt.Errorf("failed to create database connection: %w", err)
			}

			// Create runner
			runner := seed.NewRunner(gormDB, app.GetLogger(), config)

			// Run specific seeder
			ctx := context.Background()
			if err := runner.SeedSpecific(ctx, moduleName, seederName); err != nil {
				return fmt.Errorf("failed to seed %s/%s: %w", moduleName, seederName, err)
			}

			console.Success(fmt.Sprintf("Seeder '%s/%s' completed successfully", moduleName, seederName))
			return nil
		},
	}

	cmd.Flags().StringVarP(&environment, "env", "e", "dev", "Environment (dev, staging, prod)")
	cmd.Flags().BoolVar(&dryRun, "dry-run", false, "Dry run mode")
	cmd.Flags().BoolVarP(&verbose, "verbose", "v", false, "Verbose logging")

	return cmd
}

// newSeedRollbackCommand creates the seed rollback command
func newSeedRollbackCommand(app *core.AppBootstrap) *cobra.Command {
	cmd := &cobra.Command{
		Use:   "rollback",
		Short: "Rollback seeded data",
		Long:  `Rollback seeded data from modules`,
	}

	cmd.AddCommand(newSeedRollbackModuleCommand(app))
	cmd.AddCommand(newSeedRollbackSpecificCommand(app))

	return cmd
}

// newSeedRollbackModuleCommand creates the rollback module command
func newSeedRollbackModuleCommand(app *core.AppBootstrap) *cobra.Command {
	var dryRun bool

	cmd := &cobra.Command{
		Use:   "module <module_name>",
		Short: "Rollback all seeds for a module",
		Args:  cobra.ExactArgs(1),
		RunE: func(cmd *cobra.Command, args []string) error {
			moduleName := args[0]

			config := seed.LoadConfig()
			config.DryRun = dryRun

			// Initialize seed registry
			if err := initializeSeedRegistry(app); err != nil {
				return fmt.Errorf("failed to initialize seed registry: %w", err)
			}

			// Create database connection
			gormDB, err := createGormDB(app)
			if err != nil {
				return fmt.Errorf("failed to create database connection: %w", err)
			}

			// Create runner
			runner := seed.NewRunner(gormDB, app.GetLogger(), config)

			// Rollback module
			ctx := context.Background()
			if err := runner.RollbackModule(ctx, moduleName); err != nil {
				return fmt.Errorf("failed to rollback module %s: %w", moduleName, err)
			}

			console.Success(fmt.Sprintf("Module '%s' rolled back successfully", moduleName))
			return nil
		},
	}

	cmd.Flags().BoolVar(&dryRun, "dry-run", false, "Dry run mode")

	return cmd
}

// newSeedRollbackSpecificCommand creates the rollback specific command
func newSeedRollbackSpecificCommand(app *core.AppBootstrap) *cobra.Command {
	var dryRun bool

	cmd := &cobra.Command{
		Use:   "specific <module_name> <seeder_name>",
		Short: "Rollback a specific seeder",
		Args:  cobra.ExactArgs(2),
		RunE: func(cmd *cobra.Command, args []string) error {
			moduleName := args[0]
			seederName := args[1]

			config := seed.LoadConfig()
			config.DryRun = dryRun

			// Initialize seed registry
			if err := initializeSeedRegistry(app); err != nil {
				return fmt.Errorf("failed to initialize seed registry: %w", err)
			}

			// Create database connection
			gormDB, err := createGormDB(app)
			if err != nil {
				return fmt.Errorf("failed to create database connection: %w", err)
			}

			// Create runner
			runner := seed.NewRunner(gormDB, app.GetLogger(), config)

			// Rollback specific seeder
			ctx := context.Background()
			if err := runner.RollbackSpecific(ctx, moduleName, seederName); err != nil {
				return fmt.Errorf("failed to rollback %s/%s: %w", moduleName, seederName, err)
			}

			console.Success(fmt.Sprintf("Seeder '%s/%s' rolled back successfully", moduleName, seederName))
			return nil
		},
	}

	cmd.Flags().BoolVar(&dryRun, "dry-run", false, "Dry run mode")

	return cmd
}

// Helper functions

// initializeSeedRegistry initializes the seed registry with all module seeds
func initializeSeedRegistry(app *core.AppBootstrap) error {
	// Get database connection
	gormDB, err := createGormDB(app)
	if err != nil {
		return fmt.Errorf("failed to create database connection: %w", err)
	}

	// Register auth seeds
	// Note: We need to get the auth repository, but for now we'll pass nil
	// This should be improved to properly inject dependencies
	config := seed.LoadConfig()
	if err := authSeeds.RegisterAuthSeed(nil, gormDB, app.GetLogger(), config); err != nil {
		return fmt.Errorf("failed to register auth seeds: %w", err)
	}

	// NOTE: Other module seeds can be registered here when needed
	// Example: tenantSeeds.RegisterTenantSeed(nil, gormDB, app.GetLogger(), config)

	return nil
}

// createGormDB creates a GORM database connection from the app's database manager
func createGormDB(app *core.AppBootstrap) (*gorm.DB, error) {
	sqlxDB := app.GetDBManager().GetDB()
	if sqlxDB == nil {
		return nil, fmt.Errorf("database connection is nil")
	}

	// Create GORM DB from sqlx DB
	gormDB, err := gorm.Open(mysql.New(mysql.Config{
		Conn: sqlxDB.DB,
	}), &gorm.Config{})
	if err != nil {
		return nil, fmt.Errorf("failed to initialize GORM: %w", err)
	}

	return gormDB, nil
}
