package main

import (
	"context"
	"flag"
	"fmt"
	"log"
	"os"
	"path/filepath"
	"strings"
	"time"

	"wnapi/internal/core"
	"wnapi/internal/database"
	"wnapi/internal/fx/modules"
	"wnapi/internal/pkg/config/viperconfig"
	pkgdb "wnapi/internal/pkg/database"
	"wnapi/internal/pkg/logger"

	// Import modules để đăng ký factories
	_ "wnapi/modules/agent-ai"
	_ "wnapi/modules/auth"

	// _ "wnapi/modules/blog" // Temporarily disabled due to build issues
	_ "wnapi/modules/hello"
	_ "wnapi/modules/marketing"
	_ "wnapi/modules/media"
	_ "wnapi/modules/notification"
	_ "wnapi/modules/product"
	_ "wnapi/modules/rbac"

	_ "wnapi/modules/tenant"

	// Import plugins để đăng ký factories
	_ "wnapi/plugins/logger"

	// Thêm dòng này để đăng ký module seo
	_ "wnapi/modules/seo"

	"github.com/golang-migrate/migrate/v4"
	"github.com/jmoiron/sqlx"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
)

var (
	projectFlag = flag.String("project", "", "Project to run migrations for")
	configDir   = flag.String("config", ".", "Configuration directory")
	actionFlag  = flag.String("action", "up", "Migration action: up, down, version, create")
	moduleFlag  = flag.String("module", "", "Module name for create action or specific module for up/down actions")
	nameFlag    = flag.String("name", "", "Migration name for create action")
)

func main() {
	flag.Parse()

	// Handle create action
	if *actionFlag == "create" {
		if *moduleFlag == "" || *nameFlag == "" {
			log.Fatal("Module name and migration name are required for create action")
		}
		createMigration(*moduleFlag, *nameFlag)
		return
	}

	log := logger.NewConsoleLogger("migrate", logger.LevelDebug)
	log.Info("Starting database migration")

	// Thiết lập CLI mode để tự động tắt các service không cần thiết
	os.Setenv("CLI_MODE", "true")
	log.Info("Migration optimization: Enabled CLI mode to disable unnecessary services",
		"cli_mode", true)

	// Nạp cấu hình
	configFilePath := filepath.Join(*configDir, ".env")
	if *projectFlag != "" {
		projectConfigDir := filepath.Join("projects", *projectFlag)
		configFilePath = filepath.Join(projectConfigDir, ".env")
		log.Info("Using project config", "project", *projectFlag, "config", configFilePath)
	}

	configLoader := viperconfig.NewConfigLoader()
	// Thêm prefix @ để config loader xử lý đúng file .env
	if !strings.HasPrefix(configFilePath, "@") {
		configFilePath = "@" + configFilePath
	}
	cfg, err := configLoader.Load(configFilePath)
	if err != nil {
		log.Error("Failed to load config", "error", err)
		os.Exit(1)
	}

	// Tạo cấu hình database từ biến môi trường
	dbConfig := database.Config{
		Type:            cfg.GetString("DB_TYPE"),
		Host:            cfg.GetString("DB_HOST"),
		Port:            cfg.GetInt("DB_PORT"),
		Username:        cfg.GetString("DB_USERNAME"),
		Password:        cfg.GetString("DB_PASSWORD"),
		Database:        cfg.GetString("DB_DATABASE"),
		MaxOpenConns:    cfg.GetInt("DB_MAX_OPEN_CONNS"),
		MaxIdleConns:    cfg.GetInt("DB_MAX_IDLE_CONNS"),
		ConnMaxLifetime: cfg.GetDuration("DB_CONN_MAX_LIFETIME"),
		MigrationPath:   cfg.GetString("DB_MIGRATION_PATH"),
	}

	fmt.Printf("dbConfig : %s\n\n", dbConfig)

	// Thiết lập giá trị mặc định
	if dbConfig.MaxOpenConns == 0 {
		dbConfig.MaxOpenConns = 10
	}
	if dbConfig.MaxIdleConns == 0 {
		dbConfig.MaxIdleConns = 5
	}
	if dbConfig.ConnMaxLifetime == 0 {
		dbConfig.ConnMaxLifetime = time.Hour
	}
	if dbConfig.MigrationPath == "" {
		dbConfig.MigrationPath = "./migrations"
	}

	// Kết nối database
	dbManager, err := database.NewManager(dbConfig, log)
	if err != nil {
		log.Error("Failed to connect to database", "error", err)
		os.Exit(1)
	}
	defer dbManager.Close()

	// Khởi tạo app và đăng ký các module
	app, err := initApp(*projectFlag, configFilePath, dbManager, log)
	if err != nil {
		log.Error("Failed to initialize application", "error", err)
		os.Exit(1)
	}
	defer app.Shutdown(context.Background()) // Sử dụng context.Background() cho Shutdown

	// Handle migrate actions
	switch *actionFlag {
	case "up":
		if *moduleFlag != "" {
			if err := runModuleMigrationUp(dbManager.DB, dbConfig, *moduleFlag, log); err != nil {
				log.Error("Failed to run module migration", "module", *moduleFlag, "error", err)
				os.Exit(1)
			}
		} else {
			if err := runMigrationUp(dbManager.DB, dbConfig, app, log); err != nil {
				log.Error("Failed to run migrations", "error", err)
				os.Exit(1)
			}
		}
	case "down":
		if *moduleFlag != "" {
			if err := runModuleMigrationDown(dbManager.DB, dbConfig, *moduleFlag, log); err != nil {
				log.Error("Failed to rollback module migration", "module", *moduleFlag, "error", err)
				os.Exit(1)
			}
		} else {
			if err := runMigrationDown(dbManager.DB, dbConfig, app, log); err != nil {
				log.Error("Failed to rollback migrations", "error", err)
				os.Exit(1)
			}
		}
	case "version":
		if err := showMigrationVersion(dbManager.DB, dbConfig, app, log); err != nil {
			log.Error("Failed to get migration version", "error", err)
			os.Exit(1)
		}
	default:
		log.Error("Unknown action", "action", *actionFlag)
		os.Exit(1)
	}

	if *moduleFlag != "" {
		log.Info("Module migration action completed successfully", "action", *actionFlag, "module", *moduleFlag)
	} else {
		log.Info("Migration action completed successfully", "action", *actionFlag)
	}
}

// Khởi tạo app và đăng ký các module
func initApp(projectName, configPath string, dbManager *database.Manager, log logger.Logger) (*core.AppBootstrap, error) {
	// Khởi tạo config
	configLoader := viperconfig.NewConfigLoader()
	cfg, err := configLoader.Load(configPath)
	if err != nil {
		return nil, fmt.Errorf("failed to load config: %w", err)
	}

	// Khởi tạo gorm.DB từ sqlx.DB
	gormWrite, err := gorm.Open(mysql.New(mysql.Config{
		Conn: dbManager.DB.DB,
	}), &gorm.Config{})
	if err != nil {
		return nil, fmt.Errorf("failed to create write GORM DB: %w", err)
	}

	// Tạo DBManager cho AppBootstrap
	dbm := pkgdb.NewDBManager(gormWrite, nil, log)

	// Thiết lập sqlxDB để các module có thể sử dụng
	dbm.SetSqlxDB(dbManager.DB)

	// Khởi tạo AppBootstrap
	appBootstrap := core.NewAppBootstrap(
		cfg,
		log,
		dbm, // Truyền DBManager đã tạo vào đây
		nil, // cache (nếu có thể truyền)
		nil, // middlewareFactory (nếu có thể truyền)
	)

	// Khởi tạo và đăng ký các module
	if err := appBootstrap.Initialize(); err != nil {
		return nil, fmt.Errorf("failed to initialize app: %w", err)
	}

	return appBootstrap, nil
}

// createMigration tạo migration mới
func createMigration(moduleName, migrationName string) {
	generator := &database.MigrationGenerator{
		ModuleName:    moduleName,
		MigrationName: migrationName,
		BasePath:      "migrations",
	}

	if err := generator.Generate(); err != nil {
		log.Fatalf("Failed to create migration: %v", err)
	}

	fmt.Printf("Created migration files for module '%s' with name '%s'\n", moduleName, migrationName)
}

// runMigrationUp chạy migration up
func runMigrationUp(db *sqlx.DB, dbConfig database.Config, app *core.AppBootstrap, log logger.Logger) error {
	// Tạo DSN từ cấu hình
	dsn := getDSN(dbConfig)

	// Lấy db.DB từ *sqlx.DB
	sqlDB := db.DB

	// Tạo registry adapter từ FX modules
	moduleRegistryAdapter := newFXModuleRegistryAdapter(log)

	migrator, err := database.NewModuleMigrator(sqlDB, &database.MigrationConfig{
		DatabaseURL:    dsn,
		MigrationsPath: "migrations", // Thư mục migrations/ chỉ cho system
		TablePrefix:    "schema",
		Registry:       moduleRegistryAdapter,
	})
	if err != nil {
		return fmt.Errorf("failed to create migrator: %w", err)
	}
	defer migrator.Close()

	// Thêm các module đã đăng ký từ FX registry
	for _, module := range modules.GlobalRegistry.List() {
		if err := migrator.AddModule(module.Name()); err != nil {
			return fmt.Errorf("failed to add module %s: %w", module.Name(), err)
		}
	}

	// Chạy migration
	if err := migrator.MigrateUp(); err != nil {
		return fmt.Errorf("migration failed: %w", err)
	}

	return nil
}

// runMigrationDown chạy migration down
func runMigrationDown(db *sqlx.DB, dbConfig database.Config, app *core.AppBootstrap, log logger.Logger) error {
	// Tạo DSN từ cấu hình
	dsn := getDSN(dbConfig)

	// Lấy db.DB từ *sqlx.DB
	sqlDB := db.DB

	// Tạo registry adapter từ FX modules
	moduleRegistryAdapter := newFXModuleRegistryAdapter(log)

	migrator, err := database.NewModuleMigrator(sqlDB, &database.MigrationConfig{
		DatabaseURL:    dsn,
		MigrationsPath: "migrations", // Thư mục migrations/ chỉ cho system
		TablePrefix:    "schema",
		Registry:       moduleRegistryAdapter,
	})
	if err != nil {
		return fmt.Errorf("failed to create migrator: %w", err)
	}
	defer migrator.Close()

	// Thêm các module đã đăng ký từ FX registry
	for _, module := range modules.GlobalRegistry.List() {
		if err := migrator.AddModule(module.Name()); err != nil {
			return fmt.Errorf("failed to add module %s: %w", module.Name(), err)
		}
	}

	// Chạy migration down
	if err := migrator.MigrateDown(); err != nil {
		return fmt.Errorf("migration rollback failed: %w", err)
	}

	return nil
}

// runModuleMigrationUp chạy migration up cho module cụ thể
func runModuleMigrationUp(db *sqlx.DB, dbConfig database.Config, moduleName string, log logger.Logger) error {
	// Tạo DSN từ cấu hình
	dsn := getDSN(dbConfig)

	// Lấy db.DB từ *sqlx.DB
	sqlDB := db.DB

	// Kiểm tra module có tồn tại không
	module, ok := modules.GlobalRegistry.Get(moduleName)
	if !ok {
		return fmt.Errorf("module %s not found in registry", moduleName)
	}

	// Kiểm tra module có migration path không
	migrationPath := module.GetMigrationPath()
	if migrationPath == "" {
		log.Info("Module has no migrations", "module", moduleName)
		return nil
	}

	// Tạo migrator cho module cụ thể
	migrator, err := database.NewMigrator(sqlDB, &database.MigrationConfig{
		DatabaseURL:    dsn,
		MigrationsPath: migrationPath,
		TablePrefix:    fmt.Sprintf("module_%s", moduleName),
	})
	if err != nil {
		return fmt.Errorf("failed to create migrator for module %s: %w", moduleName, err)
	}
	defer func() {
		if err := migrator.Close(); err != nil {
			log.Error("Failed to close migrator", "module", moduleName, "error", err)
		}
	}()

	// Chạy migration up
	if err := migrator.Up(); err != nil && err != migrate.ErrNoChange {
		return fmt.Errorf("module %s migration failed: %w", moduleName, err)
	}

	log.Info("Module migration completed successfully", "module", moduleName, "action", "up")
	return nil
}

// runModuleMigrationDown chạy migration down cho module cụ thể
func runModuleMigrationDown(db *sqlx.DB, dbConfig database.Config, moduleName string, log logger.Logger) error {
	// Tạo DSN từ cấu hình
	dsn := getDSN(dbConfig)

	// Lấy db.DB từ *sqlx.DB
	sqlDB := db.DB

	// Kiểm tra module có tồn tại không
	module, ok := modules.GlobalRegistry.Get(moduleName)
	if !ok {
		return fmt.Errorf("module %s not found in registry", moduleName)
	}

	// Kiểm tra module có migration path không
	migrationPath := module.GetMigrationPath()
	if migrationPath == "" {
		log.Info("Module has no migrations", "module", moduleName)
		return nil
	}

	// Tạo migrator cho module cụ thể
	migrator, err := database.NewMigrator(sqlDB, &database.MigrationConfig{
		DatabaseURL:    dsn,
		MigrationsPath: migrationPath,
		TablePrefix:    fmt.Sprintf("module_%s", moduleName),
	})
	if err != nil {
		return fmt.Errorf("failed to create migrator for module %s: %w", moduleName, err)
	}
	defer func() {
		if err := migrator.Close(); err != nil {
			log.Error("Failed to close migrator", "module", moduleName, "error", err)
		}
	}()

	// Chạy migration down
	if err := migrator.Down(); err != nil && err != migrate.ErrNoChange {
		return fmt.Errorf("module %s rollback failed: %w", moduleName, err)
	}

	log.Info("Module migration rollback completed successfully", "module", moduleName, "action", "down")
	return nil
}

// showMigrationVersion hiển thị version của migration
func showMigrationVersion(db *sqlx.DB, dbConfig database.Config, app *core.AppBootstrap, log logger.Logger) error {
	log.Info("Migration version feature not implemented yet")
	return nil
}

// getDSN tạo connection string từ config
func getDSN(config database.Config) string {
	var dsn string

	switch config.Type {
	case "mysql":
		dsn = fmt.Sprintf("mysql://%s:%s@%s:%d/%s?parseTime=true",
			config.Username, config.Password, config.Host, config.Port, config.Database)
	case "postgres":
		dsn = fmt.Sprintf("postgres://%s:%s@%s:%d/%s?sslmode=disable",
			config.Username, config.Password, config.Host, config.Port, config.Database)
	default:
		dsn = fmt.Sprintf("mysql://%s:%s@%s:%d/%s",
			config.Username, config.Password, config.Host, config.Port, config.Database)
	}

	return dsn
}

// FXModuleRegistryAdapter là adapter để chuyển đổi FX modules thành database.ModuleRegistry
type FXModuleRegistryAdapter struct {
	log logger.Logger
}

func newFXModuleRegistryAdapter(log logger.Logger) *FXModuleRegistryAdapter {
	return &FXModuleRegistryAdapter{log: log}
}

func (a *FXModuleRegistryAdapter) GetModule(name string) (database.ModuleInfo, bool) {
	module, ok := modules.GlobalRegistry.Get(name)
	if !ok {
		a.log.Debug("Module not found in FX registry", "module", name)
		return nil, false
	}

	// FX modules implement the migration interface directly
	return module, true
}
