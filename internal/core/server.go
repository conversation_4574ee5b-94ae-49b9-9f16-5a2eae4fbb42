// DEPRECATED: This file has been replaced by internal/fx/lifecycle/server.go after FX migration
// All HTTP server management logic has been moved to FX lifecycle management
// This file is kept for reference and can be removed after confirming all modules work with FX

/*
package core

import (
	"context"
	"fmt"
	"net/http"
	"wnapi/internal/pkg/logger"

	"github.com/gin-gonic/gin"
)

// Server là HTTP server của ứng dụng
type Server struct {
	app    *AppBootstrap
	engine *gin.Engine
	logger logger.Logger
	server *http.Server
}

// NewServer tạo một instance mới của server
func NewServer(app *AppBootstrap) *Server {
	engine := gin.Default()

	// C<PERSON>u hình CORS cho tất cả origins
	// engine.Use(cors.New(cors.Config{
	// 	AllowOrigins:     []string{"http://localhost:9200", "http://localhost:3000", "http://127.0.0.1:9200", "http://127.0.0.1:3000"},
	// 	AllowMethods:     []string{"GET", "POST", "PUT", "PATCH", "DELETE", "OPTIONS"},
	// 	AllowHeaders:     []string{"Origin", "Content-Type", "Authorization", "Accept", "X-Requested-With", "Access-Control-Request-Method", "Access-Control-Request-Headers"},
	// 	ExposeHeaders:    []string{"Content-Length", "Access-Control-Allow-Origin"},
	// 	AllowCredentials: true,
	// 	MaxAge:           12 * 3600, // 12 hours
	// }))

	// SERVER_ADDR: Lấy địa chỉ server từ config
	serverAddr := app.Config.GetString("SERVER_ADDR")

	return &Server{
		app:    app,
		engine: engine,
		logger: app.Logger,
		server: &http.Server{
			Addr:    serverAddr,
			Handler: engine,
		},
	}
}

// Start khởi động HTTP server
func (s *Server) Start() error {
	// Thiết lập routes
	s.SetupRoutes()

	// Log routes được đăng ký
	s.logRoutes()

	// Khởi động server
	s.logger.Info("Starting HTTP server", "address", s.server.Addr)
	return s.server.ListenAndServe()
}

// Shutdown tắt server an toàn
func (s *Server) Shutdown(ctx context.Context) error {
	s.logger.Info("Shutting down HTTP server")
	return s.server.Shutdown(ctx)
}

// Group trả về một RouterGroup mới, là wrapper cho engine.Group
// Thêm để tương thích với mã hiện có
func (s *Server) Group(relativePath string) *gin.RouterGroup {
	return s.engine.Group(relativePath)
}

// GetRouter trả về gin engine, cần thiết cho một số modules
func (s *Server) GetRouter() *gin.Engine {
	return s.engine
}

// GetAppBootstrap trả về AppBootstrap instance
func (s *Server) GetAppBootstrap() *AppBootstrap {
	return s.app
}

// SetupRoutes thiết lập routes cho server
func (s *Server) SetupRoutes() {
	// Root API group
	apiV1 := s.engine.Group("/api/v1")

	// Admin API group - thêm admin routes
	adminApiV1 := s.engine.Group("/api/admin/v1")

	// Protected routes - yêu cầu xác thực và phân quyền
	protectedRoutes := apiV1.Group("")
	adminProtectedRoutes := adminApiV1.Group("")

	// 1. Áp dụng Tenant Middleware cho cả hai groups
	protectedRoutes.Use(s.tenantMiddleware())
	adminProtectedRoutes.Use(s.tenantMiddleware())

	// 2. Áp dụng Authentication Middleware cho cả hai groups
	protectedRoutes.Use(s.authMiddleware())
	adminProtectedRoutes.Use(s.authMiddleware())

	// Từ đây, các module sẽ đăng ký routes của chúng và áp dụng permission middleware
	// riêng cho từng endpoint
	s.registerModuleRoutes(protectedRoutes, adminProtectedRoutes)
}

// tenantMiddleware trả về middleware kiểm tra tenant
func (s *Server) tenantMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Stub implementation
		// Trong thực tế, middleware này sẽ kiểm tra tenant từ request
		// và đặt thông tin tenant vào context

		c.Set("tenantID", uint(1)) // Giả lập tenant ID 1
		c.Next()
	}
}

// authMiddleware trả về middleware xác thực
func (s *Server) authMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Stub implementation
		// Trong thực tế, middleware này sẽ xác thực token
		// và đặt thông tin người dùng vào context

		c.Set("userID", uint(1)) // Giả lập user ID 1
		c.Next()
	}
}

// registerModuleRoutes đăng ký routes cho các module
func (s *Server) registerModuleRoutes(parentRouter *gin.RouterGroup, adminRouter *gin.RouterGroup) {
	// Lấy danh sách modules đã được khởi tạo
	modules := s.app.GetModules()
	if len(modules) == 0 {
		s.logger.Info("No modules to register routes for")
		return
	}

	// Đăng ký routes cho từng module
	for _, module := range modules {
		s.logger.Info("Registering routes for module", "module", module.Name())

		// Đăng ký routes của module với Server
		if err := module.RegisterRoutes(s); err != nil {
			s.logger.Error("Failed to register routes for module", "module", module.Name(), "error", err.Error())
		}
	}
}

// logRoutes in ra danh sách routes đã đăng ký
func (s *Server) logRoutes() {
	routes := s.engine.Routes()
	s.logger.Info(fmt.Sprintf("Registered %d routes:", len(routes)))

	for _, route := range routes {
		s.logger.Info(fmt.Sprintf("%s %s", route.Method, route.Path))
	}
}
*/

// Placeholder package to make this file compilable
// The actual server logic is now in internal/fx/lifecycle/server.go
package core

import (
	"context"
	"net/http"
	"wnapi/internal/pkg/logger"

	"github.com/gin-gonic/gin"
)

// Stub types for backward compatibility with non-migrated modules
// These will be removed once all modules are migrated to FX

// Server stub for compatibility
type Server struct {
	app    *AppBootstrap
	engine *gin.Engine
	logger logger.Logger
	server *http.Server
}

// NewServer stub for compatibility
func NewServer(app *AppBootstrap) *Server {
	engine := gin.Default()
	// Disable automatic trailing slash redirects
	engine.RedirectTrailingSlash = false

	return &Server{
		app:    app,
		engine: engine,
		logger: app.Logger,
		server: &http.Server{},
	}
}

// Stub methods for compatibility
func (s *Server) Start() error                               { return nil }
func (s *Server) Shutdown(ctx context.Context) error         { return nil }
func (s *Server) Group(relativePath string) *gin.RouterGroup { return s.engine.Group(relativePath) }
func (s *Server) GetRouter() *gin.Engine                     { return s.engine }
func (s *Server) GetAppBootstrap() *AppBootstrap             { return s.app }
