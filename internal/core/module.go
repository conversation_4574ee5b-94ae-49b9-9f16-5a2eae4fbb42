// DEPRECATED: This file has been replaced by internal/fx/modules/ after FX migration
// All module management logic has been moved to FX module system
// This file is kept for reference and can be removed after confirming all modules work with FX

/*
package core

import (
	"context"
	"sync"
)

// Module định nghĩa interface cho các module
type Module interface {
	// Name trả về tên của module
	Name() string

	// Init khởi tạo module
	Init(ctx context.Context) error

	// RegisterRoutes đăng ký các routes của module
	RegisterRoutes(router *Server) error

	// Cleanup dọn dẹp tài nguyên của module
	Cleanup(ctx context.Context) error

	// GetMigrationPath trả về đường dẫn chứa migrations của module
	// Tr<PERSON> về chuỗi rỗng nếu module không có migrations
	GetMigrationPath() string

	// GetMigrationOrder trả về thứ tự ưu tiên khi chạy migration của module
	// Giá trị nhỏ hơn có ưu tiên cao hơn
	GetMigrationOrder() int
}

// ModuleFactory là một hàm tạo module
type ModuleFactory func(app *AppBootstrap, config map[string]interface{}) (Module, error)

// ModuleRegistry là registry quản lý các module factories
type ModuleRegistry struct {
	mu        sync.RWMutex
	factories map[string]ModuleFactory
	modules   map[string]Module
}

// GlobalModuleRegistry là registry toàn cục cho các module
var GlobalModuleRegistry = NewModuleRegistry()

// NewModuleRegistry tạo registry mới
func NewModuleRegistry() *ModuleRegistry {
	return &ModuleRegistry{
		factories: make(map[string]ModuleFactory),
		modules:   make(map[string]Module),
	}
}

// Register đăng ký module factory
func (r *ModuleRegistry) Register(name string, factory ModuleFactory) {
	if r == nil || factory == nil {
		return
	}

	r.mu.Lock()
	defer r.mu.Unlock()
	r.factories[name] = factory
}

// Get lấy module factory theo tên
func (r *ModuleRegistry) Get(name string) (ModuleFactory, bool) {
	if r == nil || name == "" {
		return nil, false
	}

	r.mu.RLock()
	defer r.mu.RUnlock()
	factory, ok := r.factories[name]
	return factory, ok
}

// RegisterModule đăng ký module instance
func (r *ModuleRegistry) RegisterModule(module Module) {
	if r == nil || module == nil {
		return
	}

	r.mu.Lock()
	defer r.mu.Unlock()
	r.modules[module.Name()] = module
}

// GetModule lấy module theo tên
func (r *ModuleRegistry) GetModule(name string) (Module, bool) {
	if r == nil || name == "" {
		return nil, false
	}

	r.mu.RLock()
	defer r.mu.RUnlock()
	module, ok := r.modules[name]
	return module, ok
}

// RegisterModuleFactory đăng ký module factory với registry toàn cục
func RegisterModuleFactory(name string, factory ModuleFactory) {
	if name == "" || factory == nil {
		return
	}
	GlobalModuleRegistry.Register(name, factory)
}

// Factories trả về map factories đã đăng ký
func (r *ModuleRegistry) Factories() map[string]ModuleFactory {
	if r == nil {
		return make(map[string]ModuleFactory)
	}

	r.mu.RLock()
	defer r.mu.RUnlock()

	result := make(map[string]ModuleFactory, len(r.factories))
	for k, v := range r.factories {
		result[k] = v
	}
	return result
}

// GetModules trả về danh sách modules đã đăng ký
func (r *ModuleRegistry) GetModules() map[string]Module {
	if r == nil {
		return make(map[string]Module)
	}

	r.mu.RLock()
	defer r.mu.RUnlock()

	result := make(map[string]Module, len(r.modules))
	for k, v := range r.modules {
		result[k] = v
	}
	return result
}

// GetEnabledModules lấy danh sách các module được kích hoạt
func (r *ModuleRegistry) GetEnabledModules(enabledModules []string) []Module {
	if r == nil {
		return nil
	}

	r.mu.RLock()
	defer r.mu.RUnlock()

	var modules []Module
	for _, name := range enabledModules {
		if module, ok := r.modules[name]; ok {
			modules = append(modules, module)
		}
	}
	return modules
}

// HasMigrations kiểm tra xem module có migrations hay không
func (r *ModuleRegistry) HasMigrations(moduleName string) bool {
	module, ok := r.GetModule(moduleName)
	if !ok {
		return false
	}
	return module.GetMigrationPath() != ""
}
*/

// Placeholder package to make this file compilable
// The actual module logic is now in internal/fx/modules/
package core

import (
	"context"
	"sync"
)

// Stub types for backward compatibility with non-migrated modules
// These will be removed once all modules are migrated to FX

// Module định nghĩa interface cho các module (stub for compatibility)
type Module interface {
	Name() string
	Init(ctx context.Context) error
	RegisterRoutes(router *Server) error
	Cleanup(ctx context.Context) error
	GetMigrationPath() string
	GetMigrationOrder() int
}

// ModuleFactory là một hàm tạo module (stub for compatibility)
type ModuleFactory func(app *AppBootstrap, config map[string]interface{}) (Module, error)

// ModuleRegistry là registry quản lý các module factories (stub for compatibility)
type ModuleRegistry struct {
	mu        sync.RWMutex
	factories map[string]ModuleFactory
	modules   map[string]Module
}

// GlobalModuleRegistry là registry toàn cục cho các module (stub for compatibility)
var GlobalModuleRegistry = NewModuleRegistry()

// NewModuleRegistry tạo registry mới (stub for compatibility)
func NewModuleRegistry() *ModuleRegistry {
	return &ModuleRegistry{
		factories: make(map[string]ModuleFactory),
		modules:   make(map[string]Module),
	}
}

// RegisterModuleFactory đăng ký module factory với registry toàn cục (stub for compatibility)
func RegisterModuleFactory(name string, factory ModuleFactory) {
	if name == "" || factory == nil {
		return
	}
	GlobalModuleRegistry.Register(name, factory)
}

// Register đăng ký module factory (stub for compatibility)
func (r *ModuleRegistry) Register(name string, factory ModuleFactory) {
	if r == nil || factory == nil {
		return
	}
	r.mu.Lock()
	defer r.mu.Unlock()
	r.factories[name] = factory
}

// Get lấy module factory theo tên (stub for compatibility)
func (r *ModuleRegistry) Get(name string) (ModuleFactory, bool) {
	if r == nil || name == "" {
		return nil, false
	}
	r.mu.RLock()
	defer r.mu.RUnlock()
	factory, ok := r.factories[name]
	return factory, ok
}

// Factories trả về map factories đã đăng ký (stub for compatibility)
func (r *ModuleRegistry) Factories() map[string]ModuleFactory {
	if r == nil {
		return make(map[string]ModuleFactory)
	}
	r.mu.RLock()
	defer r.mu.RUnlock()
	result := make(map[string]ModuleFactory, len(r.factories))
	for k, v := range r.factories {
		result[k] = v
	}
	return result
}
