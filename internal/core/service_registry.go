// internal/core/service_registry.go
package core

import (
	"fmt"
	"sync"
)

// ServiceRegistry chứa các service được chia sẻ trong toàn bộ ứng dụng.
// Nó hoạt động như một DI container đơn giản.
type ServiceRegistry struct {
	mu       sync.RWMutex
	services map[string]interface{}
}

// NewServiceRegistry tạo một thể hiện mới của ServiceRegistry.
func NewServiceRegistry() *ServiceRegistry {
	return &ServiceRegistry{
		services: make(map[string]interface{}),
	}
}

// Register đăng ký một service với một tên duy nhất.
// Nếu một service với tên đó đã tồn tại, nó sẽ báo lỗi.
func (r *ServiceRegistry) Register(name string, service interface{}) error {
	if name == "" {
		return fmt.Errorf("tên service không được để trống")
	}
	if service == nil {
		return fmt.Errorf("service không được là nil")
	}

	r.mu.Lock()
	defer r.mu.Unlock()

	if _, exists := r.services[name]; exists {
		return fmt.Errorf("service với tên '%s' đã được đăng ký", name)
	}

	r.services[name] = service
	return nil
}

// Get lấy một service đã đăng ký theo tên.
// Nó trả về service và một boolean cho biết service có tồn tại không.
func (r *ServiceRegistry) Get(name string) (interface{}, bool) {
	r.mu.RLock()
	defer r.mu.RUnlock()

	service, ok := r.services[name]
	return service, ok
}

// MustGet lấy một service đã đăng ký. Nó sẽ panic nếu service không tồn tại.
// Hữu ích cho các phụ thuộc bắt buộc phải có.
func (r *ServiceRegistry) MustGet(name string) interface{} {
	service, ok := r.Get(name)
	if !ok {
		panic(fmt.Sprintf("service được yêu cầu '%s' không được đăng ký", name))
	}
	return service
}

// GetServiceTyped là một helper function để lấy service và tự động ép kiểu.
// Nó trả về lỗi nếu service không tồn tại hoặc không thể ép kiểu.
func GetServiceTyped[T any](r *ServiceRegistry, name string) (T, error) {
	var zero T // Giá trị zero của kiểu T

	service, ok := r.Get(name)
	if !ok {
		return zero, fmt.Errorf("service '%s' không được tìm thấy", name)
	}

	typedService, ok := service.(T)
	if !ok {
		return zero, fmt.Errorf("service '%s' có kiểu không mong muốn (expected %T, got %T)", name, zero, service)
	}

	return typedService, nil
}

// ListServices trả về danh sách tên của tất cả services đã đăng ký
func (r *ServiceRegistry) ListServices() []string {
	r.mu.RLock()
	defer r.mu.RUnlock()

	names := make([]string, 0, len(r.services))
	for name := range r.services {
		names = append(names, name)
	}
	return names
}

// Count trả về số lượng services đã đăng ký
func (r *ServiceRegistry) Count() int {
	r.mu.RLock()
	defer r.mu.RUnlock()
	return len(r.services)
}
