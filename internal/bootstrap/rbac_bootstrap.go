// DEPRECATED: This file has been replaced by internal/fx/providers/permission.go after FX migration
// The RBAC bootstrap logic has been moved to FX providers for better dependency injection
// This file is kept for reference and can be removed after confirming all modules work with FX

/*
// internal/bootstrap/rbac_bootstrap.go
package bootstrap

import (
	"fmt"
	"time"

	"wnapi/internal/pkg/cache"
	"wnapi/internal/pkg/config"
	"wnapi/internal/pkg/database"
	"wnapi/internal/pkg/logger"
	"wnapi/internal/pkg/permission"
	rbacSvc "wnapi/modules/rbac/service"
)

// BootstrapRBAC khởi tạo và kết nối toàn bộ thành phần của hệ thống phân quyền RBAC
func BootstrapRBAC(
	db *database.DB,
	appCache cache.Cache,
	appLogger logger.Logger,
	cfg config.Config,
) (*permission.MiddlewareFactory, error) {
	appLogger.Info("Bắt đầu khởi tạo RBAC components")

	// <PERSON><PERSON><PERSON> tra DB trước khi sử dụng
	if db == nil {
		return nil, fmt.Errorf("database connection is nil")
	}

	if db.DB == nil {
		return nil, fmt.Errorf("database.DB.DB is nil")
	}

	appLogger.Info("Kết nối database đã được xác nhận")

	// 1. Chuẩn bị repositories (sử dụng interfaces, không phụ thuộc vào triển khai cụ thể)
	// Giả sử đã có UserRoleRepository và RolePermissionRepository

	// 2. Tạo PermissionCheckerService làm PermissionChecker
	// Đây là adapter phù hợp với interface permission.PermissionChecker
	permissionChecker := rbacSvc.NewPermissionCheckerService(appLogger)

	// 3. Tạo CachedPermissionChecker
	permissionCacheTTL := cfg.GetDurationWithDefault("cache.permission.ttl", 5*time.Minute)
	cachedPermChecker := permission.NewCachedPermissionChecker(
		permissionChecker,
		appCache,
		permissionCacheTTL,
		appLogger,
	)

	// 4. Khởi tạo MiddlewareFactory
	middlewareFactory := permission.NewMiddlewareFactory(
		cachedPermChecker,
		appLogger,
	)

	// 5. Trả về MiddlewareFactory để sử dụng ở main.go hoặc core.App
	return middlewareFactory, nil
}
*/

// Placeholder package to make this file compilable
// The actual bootstrap logic is now in internal/fx/providers/permission.go
package bootstrap
