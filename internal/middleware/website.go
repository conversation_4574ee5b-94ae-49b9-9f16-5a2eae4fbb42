package middleware

import (
	"context"
	"fmt"
	"net/http"
	"strconv"
	"wnapi/internal/pkg/auth"
	"wnapi/internal/pkg/response"

	"github.com/gin-gonic/gin"
)

// WebsiteContextKey là key để lưu thông tin website trong context
type WebsiteContextKey string

const (
	// WebsiteKey là key để lưu website trong context
	WebsiteKey WebsiteContextKey = "website"
	// WebsiteHeaderKey là key của header chứa website ID
	WebsiteHeaderKey = "X-Website-ID"
)

// WebsiteService định nghĩa interface cho website service
type WebsiteService interface {
	GetWebsite(ctx context.Context, tenantID, websiteID int) (*WebsiteResponse, error)
}

// WebsiteResponse định nghĩa response structure cho website
type WebsiteResponse struct {
	WebsiteID int `json:"website_id"`
	TenantID  int `json:"tenant_id"`
	Name      string `json:"name"`
	Status    string `json:"status"`
}

// WebsiteMiddleware tạo middleware để xác thực và lưu thông tin website vào context
// Middleware này phải được chạy sau TenantMiddleware
// Website ID được lấy từ header X-Website-ID
func WebsiteMiddleware(websiteService WebsiteService) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 1. Lấy tenant ID từ context (đã được set bởi tenant middleware)
		tenantID := auth.GetTenantID(c)
		if tenantID == 0 {
			response.Error(c, http.StatusBadRequest, "Thiếu thông tin tenant", "TENANT_REQUIRED")
			c.Abort()
			return
		}

		// 2. Lấy website ID từ header
		websiteIDStr := c.GetHeader(WebsiteHeaderKey)
		if websiteIDStr == "" {
			response.Error(c, http.StatusBadRequest, "Thiếu thông tin "+WebsiteHeaderKey, "WEBSITE_REQUIRED")
			c.Abort()
			return
		}

		// 3. Chuyển đổi website ID từ string sang uint
		websiteID, err := strconv.ParseUint(websiteIDStr, 10, 32)
		if err != nil {
			response.Error(c, http.StatusBadRequest, "Website ID không hợp lệ", "INVALID_WEBSITE_ID")
			c.Abort()
			return
		}

		// 4. Xác thực website thuộc về tenant
		website, err := websiteService.GetWebsite(c.Request.Context(), int(tenantID), int(websiteID))
		if err != nil {
			response.Error(c, http.StatusForbidden, fmt.Sprintf("Không có quyền truy cập website: %v", err), "WEBSITE_ACCESS_DENIED")
			c.Abort()
			return
		}

		// 5. Kiểm tra website có thuộc về tenant không
		if website.TenantID != int(tenantID) {
			response.Error(c, http.StatusForbidden, "Website không thuộc về tenant", "WEBSITE_TENANT_MISMATCH")
			c.Abort()
			return
		}

		// 6. Lưu thông tin website vào context
		SetWebsiteInContext(c, uint(websiteID))

		// 7. Tiếp tục xử lý request
		c.Next()
	}
}

// OptionalWebsiteMiddleware tạo middleware để xử lý website ID tùy chọn
// Nếu có X-Website-ID header thì validate, nếu không có thì bỏ qua
func OptionalWebsiteMiddleware(websiteService WebsiteService) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 1. Lấy tenant ID từ context
		tenantID := auth.GetTenantID(c)
		if tenantID == 0 {
			// Nếu không có tenant ID, tiếp tục mà không validate website
			c.Next()
			return
		}

		// 2. Lấy website ID từ header (optional)
		websiteIDStr := c.GetHeader(WebsiteHeaderKey)
		if websiteIDStr == "" {
			// Không có website ID, tiếp tục mà không set website context
			c.Next()
			return
		}

		// 3. Chuyển đổi website ID từ string sang uint
		websiteID, err := strconv.ParseUint(websiteIDStr, 10, 32)
		if err != nil {
			response.Error(c, http.StatusBadRequest, "Website ID không hợp lệ", "INVALID_WEBSITE_ID")
			c.Abort()
			return
		}

		// 4. Xác thực website thuộc về tenant
		website, err := websiteService.GetWebsite(c.Request.Context(), int(tenantID), int(websiteID))
		if err != nil {
			response.Error(c, http.StatusForbidden, fmt.Sprintf("Không có quyền truy cập website: %v", err), "WEBSITE_ACCESS_DENIED")
			c.Abort()
			return
		}

		// 5. Kiểm tra website có thuộc về tenant không
		if website.TenantID != int(tenantID) {
			response.Error(c, http.StatusForbidden, "Website không thuộc về tenant", "WEBSITE_TENANT_MISMATCH")
			c.Abort()
			return
		}

		// 6. Lưu thông tin website vào context
		SetWebsiteInContext(c, uint(websiteID))

		// 7. Tiếp tục xử lý request
		c.Next()
	}
}

// SetWebsiteInContext lưu thông tin website vào context
func SetWebsiteInContext(c *gin.Context, websiteID uint) {
	c.Set("websiteID", websiteID)
}

// GetWebsiteFromContext lấy thông tin website từ context
func GetWebsiteFromContext(c *gin.Context) (uint, error) {
	websiteValue, exists := c.Get("websiteID")
	if !exists {
		return 0, fmt.Errorf("không tìm thấy thông tin website trong context")
	}

	websiteID, ok := websiteValue.(uint)
	if !ok {
		return 0, fmt.Errorf("thông tin website không hợp lệ")
	}

	return websiteID, nil
}
