package auth

import (
	"strconv"
	"wnapi/internal/pkg/constants"

	"github.com/gin-gonic/gin"
)

// ContextKey type for context keys
type ContextKey string

// DefaultAuthMiddlewareKey is the key used to store auth data in context
const DefaultAuthMiddlewareKey = "auth"

// GetUserID lấy user ID từ context
func GetUserID(c *gin.Context) *uint {
	userID, exists := c.Get(constants.UserIDContextKey)
	if !exists {
		return nil
	}

	// Tạo biến tạm để lưu kết quả của type assertion
	value := userID.(uint)
	return &value
}

// GetTenantID lấy tenant ID từ context
// Tenant ID được lưu trong context bởi TenantMiddleware
// TenantMiddleware lấy tenant ID từ header X-Tenant-ID
func GetTenantID(c *gin.Context) uint {
	tenantID, exists := c.Get(constants.TenantContextKey)
	if !exists {
		return 0
	}

	return tenantID.(uint)
}

// GetWebsiteID lấy website ID từ context
// Website ID được lưu trong context bởi WebsiteMiddleware
func GetWebsiteID(c *gin.Context) uint {
	websiteID, exists := c.Get(constants.WebsiteContextKey)
	if !exists {
		return 0
	}

	return websiteID.(uint)
}

// parseUintFromString chuyển đổi string thành uint
func parseUintFromString(s string) (uint, error) {
	// Import strconv sẽ được thêm tự động bởi IDE
	if val, err := strconv.ParseUint(s, 10, 32); err == nil {
		return uint(val), nil
	} else {
		return 0, err
	}
}
