package models

import (
	"database/sql/driver"
	"encoding/json"
	"fmt"
	"time"

	"wnapi/modules/crawl/internal"
)

// CrawlJob represents a crawl job in the database
type CrawlJob struct {
	ID          uint                    `json:"id" gorm:"primaryKey;autoIncrement"`
	TenantID    uint                    `json:"tenant_id" gorm:"not null;index:idx_tenant_status"`
	WebsiteID   uint                    `json:"website_id" gorm:"not null;index:idx_website_tenant"`
	Name        string                  `json:"name" gorm:"size:255;not null"`
	Description string                  `json:"description" gorm:"type:text"`
	StartURL    string                  `json:"start_url" gorm:"size:500;not null"`
	Type        internal.CrawlJobType   `json:"type" gorm:"size:50;not null;default:'general'"`
	Status      internal.CrawlJobStatus `json:"status" gorm:"size:50;not null;default:'pending';index:idx_status"`
	Rules       CrawlRuleJSON           `json:"rules" gorm:"type:json"`
	Progress    CrawlProgressJSON       `json:"progress" gorm:"type:json"`
	Schedule    string                  `json:"schedule" gorm:"size:100"` // Cron expression
	Active      bool                    `json:"active" gorm:"default:true"`
	LastRunAt   *time.Time              `json:"last_run_at"`
	NextRunAt   *time.Time              `json:"next_run_at"`
	ErrorMsg    string                  `json:"error_msg" gorm:"type:text"`
	RetryCount  int                     `json:"retry_count" gorm:"default:0"`
	MaxRetries  int                     `json:"max_retries" gorm:"default:3"`
	CreatedBy   uint                    `json:"created_by" gorm:"not null"`
	CreatedAt   time.Time               `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt   time.Time               `json:"updated_at" gorm:"autoUpdateTime"`
}

// CrawlRuleJSON is a wrapper for JSON marshaling/unmarshaling of CrawlRule
type CrawlRuleJSON internal.CrawlRule

// Value implements the driver.Valuer interface for database storage
func (c CrawlRuleJSON) Value() (driver.Value, error) {
	if c.AllowedDomains == nil && c.DisallowedPaths == nil && c.Selectors == nil {
		return nil, nil
	}
	return json.Marshal(c)
}

// Scan implements the sql.Scanner interface for database retrieval
func (c *CrawlRuleJSON) Scan(value interface{}) error {
	if value == nil {
		return nil
	}

	switch v := value.(type) {
	case []byte:
		return json.Unmarshal(v, c)
	case string:
		return json.Unmarshal([]byte(v), c)
	default:
		return fmt.Errorf("cannot scan %T into CrawlRuleJSON", value)
	}
}

// CrawlProgressJSON is a wrapper for JSON marshaling/unmarshaling of CrawlProgress
type CrawlProgressJSON internal.CrawlProgress

// Value implements the driver.Valuer interface for database storage
func (c CrawlProgressJSON) Value() (driver.Value, error) {
	if c.TotalURLs == 0 && c.ProcessedURLs == 0 {
		return nil, nil
	}
	return json.Marshal(c)
}

// Scan implements the sql.Scanner interface for database retrieval
func (c *CrawlProgressJSON) Scan(value interface{}) error {
	if value == nil {
		return nil
	}

	switch v := value.(type) {
	case []byte:
		return json.Unmarshal(v, c)
	case string:
		return json.Unmarshal([]byte(v), c)
	default:
		return fmt.Errorf("cannot scan %T into CrawlProgressJSON", value)
	}
}

// TableName returns the table name for CrawlJob
func (CrawlJob) TableName() string {
	return "crawl_jobs"
}

// IsRunning checks if the crawl job is currently running
func (c *CrawlJob) IsRunning() bool {
	return c.Status == internal.CrawlJobStatusRunning
}

// CanStart checks if the crawl job can be started
func (c *CrawlJob) CanStart() bool {
	return c.Active && (c.Status == internal.CrawlJobStatusPending || c.Status == internal.CrawlJobStatusFailed)
}

// CanStop checks if the crawl job can be stopped
func (c *CrawlJob) CanStop() bool {
	return c.Status == internal.CrawlJobStatusRunning || c.Status == internal.CrawlJobStatusPaused
}

// GetProgressPercentage returns the progress percentage
func (c *CrawlJob) GetProgressPercentage() float64 {
	if c.Progress.TotalURLs == 0 {
		return 0
	}
	return float64(c.Progress.ProcessedURLs) / float64(c.Progress.TotalURLs) * 100
}