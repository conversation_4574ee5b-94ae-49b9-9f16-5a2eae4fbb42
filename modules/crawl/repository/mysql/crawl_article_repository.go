package mysql

import (
	"context"
	"fmt"
	"time"

	"gorm.io/gorm"

	"wnapi/modules/crawl/dto/request"
	"wnapi/modules/crawl/dto/response"
	"wnapi/modules/crawl/internal"
	"wnapi/modules/crawl/models"
	"wnapi/modules/crawl/repository"
)

// CrawlArticleRepository implements the CrawlArticleRepository interface
type CrawlArticleRepository struct {
	db *gorm.DB
}

// NewCrawlArticleRepository creates a new crawl article repository
func NewCrawlArticleRepository(db *gorm.DB) repository.CrawlArticleRepository {
	return &CrawlArticleRepository{
		db: db,
	}
}

// Create creates a new crawl article
func (r *CrawlArticleRepository) Create(ctx context.Context, tenantID uint, article *models.CrawlArticle) error {
	article.TenantID = tenantID

	if err := r.db.WithContext(ctx).Create(article).Error; err != nil {
		return fmt.Errorf("failed to create crawl article: %w", err)
	}

	return nil
}

// BatchCreate creates multiple crawl articles in batches
func (r *CrawlArticleRepository) BatchCreate(ctx context.Context, tenantID uint, articles []*models.CrawlArticle) error {
	if len(articles) == 0 {
		return nil
	}

	for _, article := range articles {
		article.TenantID = tenantID
	}

	if err := r.db.WithContext(ctx).CreateInBatches(articles, 100).Error; err != nil {
		return fmt.Errorf("failed to batch create crawl articles: %w", err)
	}

	return nil
}

// GetByID gets a crawl article by ID
func (r *CrawlArticleRepository) GetByID(ctx context.Context, tenantID, id uint) (*models.CrawlArticle, error) {
	var article models.CrawlArticle
	if err := r.db.WithContext(ctx).Where("tenant_id = ? AND id = ?", tenantID, id).First(&article).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, fmt.Errorf("failed to get crawl article: %w", err)
	}

	return &article, nil
}

// GetByURL gets a crawl article by URL
func (r *CrawlArticleRepository) GetByURL(ctx context.Context, tenantID uint, url string) (*models.CrawlArticle, error) {
	var article models.CrawlArticle
	if err := r.db.WithContext(ctx).
		Where("tenant_id = ? AND url = ?", tenantID, url).
		First(&article).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, fmt.Errorf("failed to get crawl article by URL: %w", err)
	}

	return &article, nil
}

// GetByHashContent gets a crawl article by content hash
func (r *CrawlArticleRepository) GetByHashContent(ctx context.Context, tenantID uint, hashContent string) (*models.CrawlArticle, error) {
	var article models.CrawlArticle
	if err := r.db.WithContext(ctx).
		Where("tenant_id = ? AND hash_content = ?", tenantID, hashContent).
		First(&article).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, fmt.Errorf("failed to get crawl article by hash content: %w", err)
	}

	return &article, nil
}

// Update updates a crawl article
func (r *CrawlArticleRepository) Update(ctx context.Context, tenantID uint, article *models.CrawlArticle) error {
	article.TenantID = tenantID

	if err := r.db.WithContext(ctx).
		Where("tenant_id = ? AND id = ?", article.TenantID, article.ID).
		Updates(article).Error; err != nil {
		return fmt.Errorf("failed to update crawl article: %w", err)
	}

	return nil
}

// Delete deletes a crawl article
func (r *CrawlArticleRepository) Delete(ctx context.Context, tenantID, id uint) error {
	if err := r.db.WithContext(ctx).Where("tenant_id = ? AND id = ?", tenantID, id).Delete(&models.CrawlArticle{}).Error; err != nil {
		return fmt.Errorf("failed to delete crawl article: %w", err)
	}

	return nil
}

// List lists crawl articles with pagination
func (r *CrawlArticleRepository) List(ctx context.Context, tenantID uint, req request.ListCrawlArticleRequest) ([]*models.CrawlArticle, error) {
	query := r.db.WithContext(ctx).Where("tenant_id = ?", tenantID)

	// Filter by crawl job if specified
	if req.CrawlJobID > 0 {
		query = query.Where("crawl_job_id = ?", req.CrawlJobID)
	}

	// Filter by status if specified
	if req.Status != "" {
		query = query.Where("status = ?", req.Status)
	}

	// Filter by language if specified
	if req.Language != "" {
		query = query.Where("language = ?", req.Language)
	}

	// Search by title if specified
	if req.Search != "" {
		query = query.Where("title LIKE ? OR content LIKE ?", "%"+req.Search+"%", "%"+req.Search+"%")
	}

	// Filter by date range
	if !req.StartDate.IsZero() {
		query = query.Where("created_at >= ?", req.StartDate)
	}
	if !req.EndDate.IsZero() {
		query = query.Where("created_at <= ?", req.EndDate)
	}

	// Apply pagination
	offset := (req.Page - 1) * req.Limit
	query = query.Offset(offset).Limit(req.Limit)

	// Apply sorting
	if req.SortBy != "" {
		order := "ASC"
		if req.SortOrder == "desc" {
			order = "DESC"
		}
		query = query.Order(fmt.Sprintf("%s %s", req.SortBy, order))
	} else {
		query = query.Order("created_at DESC")
	}

	// Execute query
	var articles []*models.CrawlArticle
	if err := query.Find(&articles).Error; err != nil {
		return nil, fmt.Errorf("failed to list crawl articles: %w", err)
	}

	return articles, nil
}

// GetByCrawlJobID gets crawl articles by crawl job ID
func (r *CrawlArticleRepository) GetByCrawlJobID(ctx context.Context, tenantID, crawlJobID uint) ([]*models.CrawlArticle, error) {
	var articles []*models.CrawlArticle
	if err := r.db.WithContext(ctx).
		Where("tenant_id = ? AND crawl_job_id = ?", tenantID, crawlJobID).
		Find(&articles).Error; err != nil {
		return nil, fmt.Errorf("failed to get crawl articles by job ID: %w", err)
	}

	return articles, nil
}

// GetByStatus gets crawl articles by status
func (r *CrawlArticleRepository) GetByStatus(ctx context.Context, tenantID uint, status string) ([]*models.CrawlArticle, error) {
	var articles []*models.CrawlArticle
	if err := r.db.WithContext(ctx).
		Where("tenant_id = ? AND status = ?", tenantID, status).
		Find(&articles).Error; err != nil {
		return nil, fmt.Errorf("failed to get crawl articles by status: %w", err)
	}

	return articles, nil
}

// Search searches crawl articles with advanced filters
func (r *CrawlArticleRepository) Search(ctx context.Context, tenantID uint, req request.SearchCrawlArticleRequest) ([]*models.CrawlArticle, error) {
	query := r.db.WithContext(ctx).Where("tenant_id = ?", tenantID)

	// Apply filters
	if req.WebsiteID > 0 {
		query = query.Where("website_id = ?", req.WebsiteID)
	}

	if req.CrawlJobID > 0 {
		query = query.Where("crawl_job_id = ?", req.CrawlJobID)
	}

	if req.Status != "" {
		query = query.Where("status = ?", req.Status)
	}

	if req.Language != "" {
		query = query.Where("language = ?", req.Language)
	}

	if req.Author != "" {
		query = query.Where("author LIKE ?", "%"+req.Author+"%")
	}

	if len(req.Tags) > 0 {
		for _, tag := range req.Tags {
			query = query.Where("JSON_CONTAINS(tags, ?)", fmt.Sprintf(`"%s"`, tag))
		}
	}

	if len(req.Categories) > 0 {
		for _, category := range req.Categories {
			query = query.Where("JSON_CONTAINS(categories, ?)", fmt.Sprintf(`"%s"`, category))
		}
	}

	// Search in title and content
	if req.Query != "" {
		query = query.Where("title LIKE ? OR content LIKE ?", "%"+req.Query+"%", "%"+req.Query+"%")
	}

	// Date range filters
	if !req.StartDate.IsZero() {
		query = query.Where("published_at >= ?", req.StartDate)
	}
	if !req.EndDate.IsZero() {
		query = query.Where("published_at <= ?", req.EndDate)
	}

	// Word count range
	if req.MinWordCount > 0 {
		query = query.Where("word_count >= ?", req.MinWordCount)
	}
	if req.MaxWordCount > 0 {
		query = query.Where("word_count <= ?", req.MaxWordCount)
	}

	// Apply pagination
	offset := (req.Page - 1) * req.Limit
	query = query.Offset(offset).Limit(req.Limit)

	// Apply sorting
	if req.SortBy != "" {
		order := "ASC"
		if req.SortOrder == "desc" {
			order = "DESC"
		}
		query = query.Order(fmt.Sprintf("%s %s", req.SortBy, order))
	} else {
		query = query.Order("published_at DESC")
	}

	// Execute query
	var articles []*models.CrawlArticle
	if err := query.Find(&articles).Error; err != nil {
		return nil, fmt.Errorf("failed to search crawl articles: %w", err)
	}

	return articles, nil
}

// UpdateStatus updates the status of a crawl article
func (r *CrawlArticleRepository) UpdateStatus(ctx context.Context, tenantID, id uint, status string) error {
	if err := r.db.WithContext(ctx).
		Model(&models.CrawlArticle{}).
		Where("tenant_id = ? AND id = ?", tenantID, id).
		Updates(map[string]interface{}{
			"status":     status,
			"updated_at": time.Now(),
		}).Error; err != nil {
		return fmt.Errorf("failed to update crawl article status: %w", err)
	}

	return nil
}

// IncrementRetryCount increments the retry count of a crawl article
func (r *CrawlArticleRepository) IncrementRetryCount(ctx context.Context, tenantID, id uint) error {
	if err := r.db.WithContext(ctx).
		Model(&models.CrawlArticle{}).
		Where("tenant_id = ? AND id = ?", tenantID, id).
		Updates(map[string]interface{}{
			"retry_count": gorm.Expr("retry_count + 1"),
			"updated_at":  time.Now(),
		}).Error; err != nil {
		return fmt.Errorf("failed to increment retry count: %w", err)
	}

	return nil
}

// BatchUpdateStatus updates status for multiple articles
func (r *CrawlArticleRepository) BatchUpdateStatus(ctx context.Context, tenantID uint, ids []uint, status string) error {
	if len(ids) == 0 {
		return nil
	}

	if err := r.db.WithContext(ctx).
		Model(&models.CrawlArticle{}).
		Where("tenant_id = ? AND id IN ?", tenantID, ids).
		Updates(map[string]interface{}{
			"status":     status,
			"updated_at": time.Now(),
		}).Error; err != nil {
		return fmt.Errorf("failed to batch update article status: %w", err)
	}

	return nil
}

// BatchDelete deletes multiple articles
func (r *CrawlArticleRepository) BatchDelete(ctx context.Context, tenantID uint, ids []uint) error {
	if len(ids) == 0 {
		return nil
	}

	if err := r.db.WithContext(ctx).
		Where("tenant_id = ? AND id IN ?", tenantID, ids).
		Delete(&models.CrawlArticle{}).Error; err != nil {
		return fmt.Errorf("failed to batch delete articles: %w", err)
	}

	return nil
}

// GetStats gets statistics for crawl articles
func (r *CrawlArticleRepository) GetStats(ctx context.Context, tenantID uint) (*response.CrawlArticleStatsResponse, error) {
	query := r.db.WithContext(ctx).Model(&models.CrawlArticle{}).Where("tenant_id = ?", tenantID)

	stats := &response.CrawlArticleStatsResponse{}

	// Count total articles
	if err := query.Count(&stats.TotalArticles).Error; err != nil {
		return nil, fmt.Errorf("failed to count total articles: %w", err)
	}

	// Count by status
	var statusCounts []struct {
		Status string
		Count  int64
	}

	if err := query.Select("status, COUNT(*) as count").
		Group("status").
		Scan(&statusCounts).Error; err != nil {
		return nil, fmt.Errorf("failed to count articles by status: %w", err)
	}

	for _, sc := range statusCounts {
		switch sc.Status {
		case string(internal.CrawlArticleStatusProcessed):
			stats.ProcessedArticles = sc.Count
		case string(internal.CrawlArticleStatusFailed):
			stats.FailedArticles = sc.Count
		case string(internal.CrawlArticleStatusPending):
			stats.PendingArticles = sc.Count
		case string(internal.CrawlArticleStatusDuplicate):
			stats.DuplicateArticles = sc.Count
		}
	}

	// Get average word count
	var avgWordCount float64
	if err := query.Select("AVG(word_count)").Scan(&avgWordCount).Error; err != nil {
		return nil, fmt.Errorf("failed to calculate average word count: %w", err)
	}
	stats.AverageWordCount = int(avgWordCount)

	return stats, nil
}

// CountByStatus counts crawl articles by status
func (r *CrawlArticleRepository) CountByStatus(ctx context.Context, tenantID uint, status string) (int64, error) {
	var count int64
	if err := r.db.WithContext(ctx).
		Model(&models.CrawlArticle{}).
		Where("tenant_id = ? AND status = ?", tenantID, status).
		Count(&count).Error; err != nil {
		return 0, fmt.Errorf("failed to count crawl articles by status: %w", err)
	}

	return count, nil
}

// CleanupOldArticles deletes articles older than the specified duration
func (r *CrawlArticleRepository) CleanupOldArticles(ctx context.Context, tenantID uint, olderThan time.Duration) (int64, error) {
	cutoffTime := time.Now().Add(-olderThan)

	result := r.db.WithContext(ctx).
		Where("tenant_id = ? AND created_at < ?", tenantID, cutoffTime).
		Delete(&models.CrawlArticle{})

	if result.Error != nil {
		return 0, fmt.Errorf("failed to cleanup old articles: %w", result.Error)
	}

	return result.RowsAffected, nil
}

// CleanupFailedArticles deletes failed articles older than the specified duration
func (r *CrawlArticleRepository) CleanupFailedArticles(ctx context.Context, tenantID uint, olderThan time.Duration) (int64, error) {
	cutoffTime := time.Now().Add(-olderThan)

	result := r.db.WithContext(ctx).
		Where("tenant_id = ? AND status = ? AND created_at < ?", tenantID, string(internal.CrawlArticleStatusFailed), cutoffTime).
		Delete(&models.CrawlArticle{})

	if result.Error != nil {
		return 0, fmt.Errorf("failed to cleanup failed articles: %w", result.Error)
	}

	return result.RowsAffected, nil
}

// BulkUpdate updates multiple crawl articles
func (r *CrawlArticleRepository) BulkUpdate(ctx context.Context, tenantID uint, ids []uint, updates map[string]interface{}) error {
	if len(ids) == 0 {
		return nil
	}

	updates["updated_at"] = time.Now()

	if err := r.db.WithContext(ctx).
		Model(&models.CrawlArticle{}).
		Where("tenant_id = ? AND id IN ?", tenantID, ids).
		Updates(updates).Error; err != nil {
		return fmt.Errorf("failed to bulk update crawl articles: %w", err)
	}

	return nil
}