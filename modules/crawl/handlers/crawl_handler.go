package handlers

import (
	"fmt"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"

	"wnapi/modules/crawl/dto/request"
	"wnapi/modules/crawl/dto/response"
	"wnapi/modules/crawl/services"
	"wnapi/pkg/middleware"
	pkgresponse "wnapi/pkg/response"
)

// CrawlHandler handles crawl-related HTTP requests
type CrawlHandler struct {
	crawlJobService     *services.CrawlJobService
	crawlArticleService *services.CrawlArticleService
	crawlService        *services.CrawlService
}

// NewCrawlHandler creates a new crawl handler
func NewCrawlHandler(
	crawlJobService *services.CrawlJobService,
	crawlArticleService *services.CrawlArticleService,
	crawlService *services.CrawlService,
) *CrawlHandler {
	return &CrawlHandler{
		crawlJobService:     crawlJobService,
		crawlArticleService: crawlArticleService,
		crawlService:        crawlService,
	}
}

// Crawl Job Endpoints

// CreateCrawlJob creates a new crawl job
func (h *CrawlHandler) CreateCrawlJob(c *gin.Context) {
	var req request.CreateCrawlJobRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, pkgresponse.ErrorResponse{
			Error:   "Invalid request",
			Message: err.Error(),
		})
		return
	}

	// Get tenant ID from context
	tenantID := middleware.GetTenantID(c)

	crawlJob, err := h.crawlJobService.Create(c.Request.Context(), tenantID, req)
	if err != nil {
		fmt.Printf("Failed to create crawl job: %v\n", err)
		c.JSON(http.StatusInternalServerError, pkgresponse.ErrorResponse{
			Error:   "Failed to create crawl job",
			Message: err.Error(),
		})
		return
	}

	c.JSON(http.StatusCreated, pkgresponse.SuccessResponse{
		Message: "Crawl job created successfully",
		Data:    crawlJob,
	})
}

// GetCrawlJob gets a crawl job by ID
func (h *CrawlHandler) GetCrawlJob(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, pkgresponse.ErrorResponse{
			Error:   "Invalid ID",
			Message: "ID must be a valid number",
		})
		return
	}

	tenantID := middleware.GetTenantID(c)

	crawlJob, err := h.crawlJobService.GetByID(c.Request.Context(), tenantID, uint(id))
	if err != nil {
		fmt.Printf("Failed to get crawl job: %v\n", err)
		c.JSON(http.StatusInternalServerError, pkgresponse.ErrorResponse{
			Error:   "Failed to get crawl job",
			Message: err.Error(),
		})
		return
	}

	if crawlJob == nil {
		c.JSON(http.StatusNotFound, pkgresponse.ErrorResponse{
			Error:   "Not found",
			Message: "Crawl job not found",
		})
		return
	}

	c.JSON(http.StatusOK, pkgresponse.SuccessResponse{
		Data: crawlJob,
	})
}

// ListCrawlJobs lists crawl jobs with pagination
func (h *CrawlHandler) ListCrawlJobs(c *gin.Context) {
	var req request.ListCrawlJobRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		c.JSON(http.StatusBadRequest, pkgresponse.ErrorResponse{
			Error:   "Invalid query parameters",
			Message: err.Error(),
		})
		return
	}

	// Set defaults
	if req.Page == 0 {
		req.Page = 1
	}
	if req.Limit == 0 {
		req.Limit = 20
	}

	tenantID := middleware.GetTenantID(c)

	crawlJobs, total, err := h.crawlJobService.List(c.Request.Context(), tenantID, req)
	if err != nil {
		fmt.Printf("Failed to list crawl jobs: %v\n", err)
		c.JSON(http.StatusInternalServerError, pkgresponse.ErrorResponse{
			Error:   "Failed to list crawl jobs",
			Message: err.Error(),
		})
		return
	}

	listResponse := response.CrawlJobListResponse{
		Data: crawlJobs,
		Meta: response.Meta{
			Page:       req.Page,
			Limit:      req.Limit,
			Total:      total,
			TotalPages: (total + int64(req.Limit) - 1) / int64(req.Limit),
		},
	}

	c.JSON(http.StatusOK, listResponse)
}

// UpdateCrawlJob updates a crawl job
func (h *CrawlHandler) UpdateCrawlJob(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, pkgresponse.ErrorResponse{
			Error:   "Invalid ID",
			Message: "ID must be a valid number",
		})
		return
	}

	var req request.UpdateCrawlJobRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, pkgresponse.ErrorResponse{
			Error:   "Invalid request",
			Message: err.Error(),
		})
		return
	}

	tenantID := middleware.GetTenantID(c)

	crawlJob, err := h.crawlJobService.Update(c.Request.Context(), tenantID, uint(id), req)
	if err != nil {
		fmt.Printf("Failed to update crawl job: %v\n", err)
		c.JSON(http.StatusInternalServerError, pkgresponse.ErrorResponse{
			Error:   "Failed to update crawl job",
			Message: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, pkgresponse.SuccessResponse{
		Message: "Crawl job updated successfully",
		Data:    crawlJob,
	})
}

// DeleteCrawlJob deletes a crawl job
func (h *CrawlHandler) DeleteCrawlJob(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, pkgresponse.ErrorResponse{
			Error:   "Invalid ID",
			Message: "ID must be a valid number",
		})
		return
	}

	tenantID := middleware.GetTenantID(c)

	err = h.crawlJobService.Delete(c.Request.Context(), tenantID, uint(id))
	if err != nil {
		fmt.Printf("Failed to delete crawl job: %v\n", err)
		c.JSON(http.StatusInternalServerError, pkgresponse.ErrorResponse{
			Error:   "Failed to delete crawl job",
			Message: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, pkgresponse.SuccessResponse{
		Message: "Crawl job deleted successfully",
	})
}

// StartCrawlJob starts a crawl job
func (h *CrawlHandler) StartCrawlJob(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, pkgresponse.ErrorResponse{
			Error:   "Invalid ID",
			Message: "ID must be a valid number",
		})
		return
	}

	tenantID := middleware.GetTenantID(c)

	err = h.crawlJobService.Start(c.Request.Context(), tenantID, uint(id))
	if err != nil {
		fmt.Printf("Failed to start crawl job: %v\n", err)
		c.JSON(http.StatusInternalServerError, pkgresponse.ErrorResponse{
			Error:   "Failed to start crawl job",
			Message: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, pkgresponse.SuccessResponse{
		Message: "Crawl job started successfully",
	})
}

// StopCrawlJob stops a crawl job
func (h *CrawlHandler) StopCrawlJob(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, pkgresponse.ErrorResponse{
			Error:   "Invalid ID",
			Message: "ID must be a valid number",
		})
		return
	}

	tenantID := middleware.GetTenantID(c)

	req := request.StopCrawlJobRequest{}
	err = h.crawlJobService.Stop(c.Request.Context(), tenantID, uint(id), req)
	if err != nil {
		fmt.Printf("Failed to stop crawl job: %v\n", err)
		c.JSON(http.StatusInternalServerError, pkgresponse.ErrorResponse{
			Error:   "Failed to stop crawl job",
			Message: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, pkgresponse.SuccessResponse{
		Message: "Crawl job stopped successfully",
	})
}

// GetCrawlJobProgress gets crawl job progress
func (h *CrawlHandler) GetCrawlJobProgress(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, pkgresponse.ErrorResponse{
			Error:   "Invalid ID",
			Message: "ID must be a valid number",
		})
		return
	}

	tenantID := middleware.GetTenantID(c)

	progress, err := h.crawlService.GetCrawlProgress(c.Request.Context(), tenantID, uint(id))
	if err != nil {
		fmt.Printf("Failed to get crawl progress: %v\n", err)
		c.JSON(http.StatusInternalServerError, pkgresponse.ErrorResponse{
			Error:   "Failed to get crawl progress",
			Message: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, pkgresponse.SuccessResponse{
		Data: progress,
	})
}

// TestCrawl tests a crawl configuration
func (h *CrawlHandler) TestCrawl(c *gin.Context) {
	var req request.TestCrawlJobRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, pkgresponse.ErrorResponse{
			Error:   "Invalid request",
			Message: err.Error(),
		})
		return
	}

	tenantID := middleware.GetTenantID(c)

	result, err := h.crawlService.TestCrawl(c.Request.Context(), tenantID, req)
	if err != nil {
		fmt.Printf("Failed to test crawl: %v\n", err)
		c.JSON(http.StatusInternalServerError, pkgresponse.ErrorResponse{
			Error:   "Failed to test crawl",
			Message: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, pkgresponse.SuccessResponse{
		Data: result,
	})
}

// Crawl Article Endpoints

// GetCrawlArticle gets a crawl article by ID
func (h *CrawlHandler) GetCrawlArticle(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, pkgresponse.ErrorResponse{
			Error:   "Invalid ID",
			Message: "ID must be a valid number",
		})
		return
	}

	tenantID := middleware.GetTenantID(c)

	article, err := h.crawlArticleService.GetByID(c.Request.Context(), tenantID, uint(id))
	if err != nil {
		fmt.Printf("Failed to get crawl article: %v\n", err)
		c.JSON(http.StatusInternalServerError, pkgresponse.ErrorResponse{
			Error:   "Failed to get crawl article",
			Message: err.Error(),
		})
		return
	}

	if article == nil {
		c.JSON(http.StatusNotFound, pkgresponse.ErrorResponse{
			Error:   "Not found",
			Message: "Crawl article not found",
		})
		return
	}

	c.JSON(http.StatusOK, pkgresponse.SuccessResponse{
		Data: article,
	})
}

// ListCrawlArticles lists crawl articles with pagination
func (h *CrawlHandler) ListCrawlArticles(c *gin.Context) {
	var req request.ListCrawlArticleRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		c.JSON(http.StatusBadRequest, pkgresponse.ErrorResponse{
			Error:   "Invalid query parameters",
			Message: err.Error(),
		})
		return
	}

	// Set defaults
	if req.Page == 0 {
		req.Page = 1
	}
	if req.Limit == 0 {
		req.Limit = 20
	}

	tenantID := middleware.GetTenantID(c)

	articles, total, err := h.crawlArticleService.List(c.Request.Context(), tenantID, req)
	if err != nil {
		fmt.Printf("Failed to list crawl articles: %v\n", err)
		c.JSON(http.StatusInternalServerError, pkgresponse.ErrorResponse{
			Error:   "Failed to list crawl articles",
			Message: err.Error(),
		})
		return
	}

	listResponse := response.CrawlArticleListResponse{
		Data: articles,
		Meta: response.Meta{
			Page:       req.Page,
			Limit:      req.Limit,
			Total:      total,
			TotalPages: (total + int64(req.Limit) - 1) / int64(req.Limit),
		},
	}

	c.JSON(http.StatusOK, listResponse)
}

// SearchCrawlArticles searches crawl articles
func (h *CrawlHandler) SearchCrawlArticles(c *gin.Context) {
	var req request.SearchCrawlArticleRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		c.JSON(http.StatusBadRequest, pkgresponse.ErrorResponse{
			Error:   "Invalid query parameters",
			Message: err.Error(),
		})
		return
	}

	// Set defaults
	if req.Page == 0 {
		req.Page = 1
	}
	if req.Limit == 0 {
		req.Limit = 20
	}

	tenantID := middleware.GetTenantID(c)

	articles, total, err := h.crawlArticleService.Search(c.Request.Context(), tenantID, req)
	if err != nil {
		fmt.Printf("Failed to search crawl articles: %v\n", err)
		c.JSON(http.StatusInternalServerError, pkgresponse.ErrorResponse{
			Error:   "Failed to search crawl articles",
			Message: err.Error(),
		})
		return
	}

	searchResponse := response.CrawlArticleListResponse{
		Data: articles,
		Meta: response.Meta{
			Page:       req.Page,
			Limit:      req.Limit,
			Total:      total,
			TotalPages: (total + int64(req.Limit) - 1) / int64(req.Limit),
		},
	}

	c.JSON(http.StatusOK, searchResponse)
}

// UpdateCrawlArticle updates a crawl article
func (h *CrawlHandler) UpdateCrawlArticle(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, pkgresponse.ErrorResponse{
			Error:   "Invalid ID",
			Message: "ID must be a valid number",
		})
		return
	}

	var req request.UpdateCrawlArticleRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, pkgresponse.ErrorResponse{
			Error:   "Invalid request",
			Message: err.Error(),
		})
		return
	}

	tenantID := middleware.GetTenantID(c)

	article, err := h.crawlArticleService.Update(c.Request.Context(), tenantID, uint(id), req)
	if err != nil {
		fmt.Printf("Failed to update crawl article: %v\n", err)
		c.JSON(http.StatusInternalServerError, pkgresponse.ErrorResponse{
			Error:   "Failed to update crawl article",
			Message: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, pkgresponse.SuccessResponse{
		Message: "Crawl article updated successfully",
		Data:    article,
	})
}

// DeleteCrawlArticle deletes a crawl article
func (h *CrawlHandler) DeleteCrawlArticle(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, pkgresponse.ErrorResponse{
			Error:   "Invalid ID",
			Message: "ID must be a valid number",
		})
		return
	}

	tenantID := middleware.GetTenantID(c)

	err = h.crawlArticleService.Delete(c.Request.Context(), tenantID, uint(id))
	if err != nil {
		fmt.Printf("Failed to delete crawl article: %v\n", err)
		c.JSON(http.StatusInternalServerError, pkgresponse.ErrorResponse{
			Error:   "Failed to delete crawl article",
			Message: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, pkgresponse.SuccessResponse{
		Message: "Crawl article deleted successfully",
	})
}

// GetCrawlStats gets crawl statistics
func (h *CrawlHandler) GetCrawlStats(c *gin.Context) {
	tenantID := middleware.GetTenantID(c)

	jobStats, err := h.crawlJobService.GetStats(c.Request.Context(), tenantID)
	if err != nil {
		fmt.Printf("Failed to get job stats: %v\n", err)
		c.JSON(http.StatusInternalServerError, pkgresponse.ErrorResponse{
			Error:   "Failed to get crawl stats",
			Message: err.Error(),
		})
		return
	}

	articleStats, err := h.crawlArticleService.GetStats(c.Request.Context(), tenantID)
	if err != nil {
		fmt.Printf("Failed to get article stats: %v\n", err)
		c.JSON(http.StatusInternalServerError, pkgresponse.ErrorResponse{
			Error:   "Failed to get crawl stats",
			Message: err.Error(),
		})
		return
	}

	stats := response.CrawlSystemStatsResponse{
		Jobs:     *jobStats,
		Articles: *articleStats,
	}

	c.JSON(http.StatusOK, pkgresponse.SuccessResponse{
		Data: stats,
	})
}