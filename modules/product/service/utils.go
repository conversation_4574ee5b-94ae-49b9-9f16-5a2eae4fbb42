package service

import (
	"context"
	"wnapi/internal/pkg/constants"
)

// getUserIDFromContext extracts the user ID from context
func getUserIDFromContext(ctx context.Context) int64 {
	// Get user ID from context if available
	userIDValue := ctx.Value(constants.UserIDContextKey)
	if userIDValue != nil {
		// Convert to int64 if possible
		if userID, ok := userIDValue.(uint); ok {
			return int64(userID)
		}
		if userID, ok := userIDValue.(int); ok {
			return int64(userID)
		}
	}

	// For testing purposes, return a default user ID
	return 1
}

// <PERSON><PERSON>c hàm tiện ích khác có thể được thêm vào đây trong tương lai
