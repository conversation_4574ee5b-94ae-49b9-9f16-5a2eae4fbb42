package handlers

import (
	"errors"
	"net/http"
	"strconv"
	"strings"

	"wnapi/internal/pkg/auth"
	"wnapi/internal/pkg/response"

	"github.com/gin-gonic/gin"
)

// Constants for error codes
const (
	ErrCodeNotFound      = "NOT_FOUND"
	ErrCodeInvalidInput  = "INVALID_INPUT"
	ErrCodeDuplicateSlug = "DUPLICATE_SLUG"
	ErrCodeServerError   = "SERVER_ERROR"
)

// MetaResponse định nghĩa metadata cho API response
type MetaResponse struct {
	NextCursor string `json:"next_cursor,omitempty"`
	HasMore    bool   `json:"has_more"`
	Total      int    `json:"total,omitempty"`
}

// StatusResponse định nghĩa thông tin status cho API response
type StatusResponse struct {
	Code      int         `json:"code"`
	Message   string      `json:"message"`
	Success   bool        `json:"success"`
	ErrorCode string      `json:"error_code,omitempty"`
	Path      string      `json:"path"`
	Timestamp string      `json:"timestamp"`
	Details   interface{} `json:"details,omitempty"`
}

// Response định nghĩa cấu trúc chuẩn cho API response
type Response struct {
	Status StatusResponse `json:"status"`
	Data   interface{}    `json:"data,omitempty"`
	Meta   *MetaResponse  `json:"meta,omitempty"`
}

// getTenantIDFromContext lấy tenant ID từ context của request
func getTenantIDFromContext(c *gin.Context) (int, error) {
	tenantID := auth.GetTenantID(c)
	if tenantID == 0 {
		return 0, errors.New("không tìm thấy tenant ID trong context")
	}
	return int(tenantID), nil
}

// getUserIDFromContext lấy user ID từ context
func getUserIDFromContext(c *gin.Context) int {
	userIDPtr := auth.GetUserID(c)
	if userIDPtr == nil {
		return 0
	}
	return int(*userIDPtr)
}

// apiSuccess gửi response API thành công
func apiSuccess(c *gin.Context, statusCode int, message string, data interface{}) {
	response.Success(c, data, nil)
}

// apiSuccessWithMeta gửi response API thành công kèm metadata
func apiSuccessWithMeta(c *gin.Context, statusCode int, message string, data interface{}, meta map[string]interface{}) {
	responseMeta := &response.Meta{}
	if nextCursor, ok := meta["next_cursor"].(string); ok {
		responseMeta.NextCursor = nextCursor
	}
	if hasMore, ok := meta["has_more"].(bool); ok {
		responseMeta.HasMore = hasMore
	}
	response.Success(c, data, responseMeta)
}

// apiError gửi response API lỗi
func apiError(c *gin.Context, statusCode int, errorCode, message string) {
	response.ErrorWithDetails(c, statusCode, message, errorCode, nil)
}

// apiErrorWithDetails gửi response API lỗi kèm chi tiết
func apiErrorWithDetails(c *gin.Context, statusCode int, errorCode, message string, details interface{}) {
	response.ErrorWithDetails(c, statusCode, message, errorCode, details)
}

// getIDParam lấy ID từ path parameter
func getIDParam(c *gin.Context) (int, error) {
	idStr := c.Param("id")
	if idStr == "" {
		return 0, errors.New("ID không được cung cấp")
	}

	id, err := strconv.Atoi(idStr)
	if err != nil {
		return 0, errors.New("ID không hợp lệ")
	}

	return id, nil
}

// getWebsiteIDParam lấy website ID từ path parameter
func getWebsiteIDParam(c *gin.Context) (int, error) {
	idStr := c.Param("website_id")
	if idStr == "" {
		return 0, errors.New("ID website không được cung cấp")
	}

	id, err := strconv.Atoi(idStr)
	if err != nil {
		return 0, errors.New("ID website không hợp lệ")
	}

	return id, nil
}

// getThemeIDParam lấy theme ID từ path parameter
func getThemeIDParam(c *gin.Context) (int, error) {
	idStr := c.Param("theme_id")
	if idStr == "" {
		return 0, errors.New("ID theme không được cung cấp")
	}

	id, err := strconv.Atoi(idStr)
	if err != nil {
		return 0, errors.New("ID theme không hợp lệ")
	}

	return id, nil
}

// handleDBError xử lý lỗi từ database
func handleDBError(c *gin.Context, err error) {
	switch {
	case strings.Contains(err.Error(), "Duplicate entry") && strings.Contains(err.Error(), "for key 'slug'"):
		apiError(c, http.StatusConflict, ErrCodeDuplicateSlug, "Slug đã tồn tại")
	case strings.Contains(err.Error(), "record not found") || err.Error() == "sql: no rows in result set":
		apiError(c, http.StatusNotFound, ErrCodeNotFound, "Không tìm thấy dữ liệu")
	default:
		details := []interface{}{
			map[string]string{
				"message": err.Error(),
			},
		}
		apiErrorWithDetails(c, http.StatusInternalServerError, ErrCodeServerError, "Lỗi server", details)
	}
}

// handleInvalidInput xử lý lỗi input không hợp lệ
func handleInvalidInput(c *gin.Context, err error) {
	details := []interface{}{
		map[string]string{
			"message": err.Error(),
		},
	}
	apiErrorWithDetails(c, http.StatusBadRequest, ErrCodeInvalidInput, "Dữ liệu đầu vào không hợp lệ", details)
}

// handlePageNotFound xử lý lỗi không tìm thấy trang
func handlePageNotFound(c *gin.Context, pageID int) {
	details := []interface{}{
		map[string]string{
			"message": "Không tìm thấy trang có ID " + strconv.Itoa(pageID),
		},
	}
	apiErrorWithDetails(c, http.StatusNotFound, ErrCodeNotFound, "Không tìm thấy trang", details)
}

// handleWebsiteNotFound xử lý lỗi không tìm thấy website
func handleWebsiteNotFound(c *gin.Context, websiteID int) {
	details := []interface{}{
		map[string]string{
			"message": "Không tìm thấy website có ID " + strconv.Itoa(websiteID),
		},
	}
	apiErrorWithDetails(c, http.StatusNotFound, ErrCodeNotFound, "Không tìm thấy website", details)
}

// handleThemeNotFound xử lý lỗi không tìm thấy theme
func handleThemeNotFound(c *gin.Context, themeID int) {
	details := []interface{}{
		map[string]string{
			"message": "Không tìm thấy theme có ID " + strconv.Itoa(themeID),
		},
	}
	apiErrorWithDetails(c, http.StatusNotFound, ErrCodeNotFound, "Không tìm thấy theme", details)
}

// handleTemplateNotFound xử lý lỗi không tìm thấy template
func handleTemplateNotFound(c *gin.Context, templateID int) {
	details := []interface{}{
		map[string]string{
			"message": "Không tìm thấy template có ID " + strconv.Itoa(templateID),
		},
	}
	apiErrorWithDetails(c, http.StatusNotFound, ErrCodeNotFound, "Không tìm thấy template", details)
}
