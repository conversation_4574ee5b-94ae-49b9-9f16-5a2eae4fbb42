-- Add tenant_id to website_pages table
ALTER TABLE website_pages 
ADD COLUMN tenant_id INT UNSIGNED NOT NULL AFTER page_id,
ADD INDEX idx_tenant_id (tenant_id);

-- Add tenant_id and website_id to website_menus table (if exists)
-- Note: This table might not exist yet, so we check first
CREATE TABLE IF NOT EXISTS website_menus (
  id BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
  tenant_id INT UNSIGNED NOT NULL,
  website_id INT UNSIGNED NOT NULL,
  name VARCHAR(100) NOT NULL,
  position VARCHAR(50) NOT NULL,
  is_active BOOLEAN NOT NULL DEFAULT TRUE,
  created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (id),
  INDEX idx_tenant_id (tenant_id),
  INDEX idx_website_id (website_id),
  INDEX idx_position (position)
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci;

-- Add tenant_id and website_id to website_menu_items table (if exists)
CREATE TABLE IF NOT EXISTS website_menu_items (
  id BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
  tenant_id INT UNSIGNED NOT NULL,
  website_id INT UNSIGNED NOT NULL,
  menu_id BIGINT UNSIGNED NOT NULL,
  parent_id BIGINT UNSIGNED NULL,
  title VARCHAR(100) NOT NULL,
  url VARCHAR(255) NULL,
  target VARCHAR(20) NOT NULL DEFAULT '_self',
  `order` INT NOT NULL DEFAULT 0,
  is_active BOOLEAN NOT NULL DEFAULT TRUE,
  created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (id),
  INDEX idx_tenant_id (tenant_id),
  INDEX idx_website_id (website_id),
  INDEX idx_menu_id (menu_id),
  INDEX idx_parent_id (parent_id)
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci;

-- Add tenant_id and website_id to website_banners table (if exists)
CREATE TABLE IF NOT EXISTS website_banners (
  id BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
  tenant_id INT UNSIGNED NOT NULL,
  website_id INT UNSIGNED NOT NULL,
  title VARCHAR(200) NOT NULL,
  image_url VARCHAR(255) NOT NULL,
  link_url VARCHAR(255) NULL,
  position VARCHAR(50) NOT NULL,
  `order` INT NOT NULL DEFAULT 0,
  is_active BOOLEAN NOT NULL DEFAULT TRUE,
  start_date TIMESTAMP NULL,
  end_date TIMESTAMP NULL,
  created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (id),
  INDEX idx_tenant_id (tenant_id),
  INDEX idx_website_id (website_id),
  INDEX idx_position (position),
  INDEX idx_is_active (is_active)
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci;
