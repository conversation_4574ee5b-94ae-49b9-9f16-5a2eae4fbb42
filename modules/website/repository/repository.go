package repository

import (
	"context"
	"errors"
	"fmt"
	"strings"
	"time"

	"wnapi/internal/pkg/auth"
	"wnapi/internal/pkg/database"
	"wnapi/internal/pkg/logger"
	"wnapi/internal/pkg/tracing"
	"wnapi/modules/website/dto"
	"wnapi/modules/website/internal"

	"github.com/gin-gonic/gin"
	"github.com/gosimple/slug"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

// mysqlRepository triển khai Repository interface sử dụng GORM
type mysqlRepository struct {
	db     *gorm.DB
	logger logger.Logger
}

// NewMySQLRepository tạo một repository mới
func NewMySQLRepository(dbManager *database.DBManager, logger logger.Logger) (internal.Repository, error) {
	if dbManager == nil {
		return nil, errors.New("database manager không được để trống")
	}

	sqlxDB := dbManager.GetDB()
	if sqlxDB == nil {
		return nil, errors.New("không thể kết nối tới cơ sở dữ liệu")
	}

	// Create GORM DB from sqlx DB
	gormDB, err := gorm.Open(mysql.New(mysql.Config{
		Conn: sqlxDB.DB,
	}), &gorm.Config{})
	if err != nil {
		return nil, fmt.Errorf("failed to initialize GORM: %w", err)
	}

	return &mysqlRepository{
		db:     gormDB,
		logger: logger,
	}, nil
}

// Page methods
// ---------------------------------

// CreatePage tạo trang mới
func (r *mysqlRepository) CreatePage(ctx context.Context, page *internal.Page) error {
	ctx, span := tracing.DatabaseMiddleware(ctx, "INSERT", "website_pages")
	defer span.End()

	// Tạo slug từ tiêu đề
	if page.Slug == "" {
		page.Slug = slug.Make(page.Title)
	}

	// Set tenant_id and website_id from context if not already set
	if page.TenantID == 0 {
		if ginCtx, ok := ctx.(*gin.Context); ok {
			page.TenantID = uint(auth.GetTenantID(ginCtx))
			page.WebsiteID = uint(auth.GetWebsiteID(ginCtx))
		}
	}

	result := r.db.WithContext(ctx).Create(page)
	if result.Error != nil {
		r.logger.Error("Không thể tạo trang", logger.String("error", result.Error.Error()))
		tracing.RecordError(ctx, result.Error)
		return internal.ErrDatabaseError
	}

	return nil
}

// GetPageByID lấy trang theo ID
func (r *mysqlRepository) GetPageByID(ctx context.Context, id int64) (*internal.Page, error) {
	ctx, span := tracing.DatabaseMiddleware(ctx, "SELECT", "website_pages")
	defer span.End()

	var page internal.Page
	result := r.db.WithContext(ctx).First(&page, id)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return nil, internal.ErrPageNotFound
		}
		r.logger.Error("Không thể lấy trang", logger.String("error", result.Error.Error()))
		tracing.RecordError(ctx, result.Error)
		return nil, internal.ErrDatabaseError
	}

	return &page, nil
}

// GetPageBySlug lấy trang theo slug
func (r *mysqlRepository) GetPageBySlug(ctx context.Context, slug string) (*internal.Page, error) {
	ctx, span := tracing.DatabaseMiddleware(ctx, "SELECT", "website_pages")
	defer span.End()

	var page internal.Page
	result := r.db.WithContext(ctx).Where("slug = ?", slug).First(&page)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return nil, internal.ErrPageNotFound
		}
		r.logger.Error("Không thể lấy trang theo slug", logger.String("error", result.Error.Error()))
		tracing.RecordError(ctx, result.Error)
		return nil, internal.ErrDatabaseError
	}

	return &page, nil
}

// UpdatePage cập nhật trang
func (r *mysqlRepository) UpdatePage(ctx context.Context, page *internal.Page) error {
	ctx, span := tracing.DatabaseMiddleware(ctx, "UPDATE", "website_pages")
	defer span.End()

	// Tạo slug từ tiêu đề nếu cần
	if page.Slug == "" && page.Title != "" {
		page.Slug = slug.Make(page.Title)
	}

	result := r.db.WithContext(ctx).Save(page)
	if result.Error != nil {
		r.logger.Error("Không thể cập nhật trang", logger.String("error", result.Error.Error()))
		tracing.RecordError(ctx, result.Error)
		return internal.ErrDatabaseError
	}

	if result.RowsAffected == 0 {
		return internal.ErrPageNotFound
	}

	return nil
}

// DeletePage xóa trang
func (r *mysqlRepository) DeletePage(ctx context.Context, id int64) error {
	ctx, span := tracing.DatabaseMiddleware(ctx, "DELETE", "website_pages")
	defer span.End()

	result := r.db.WithContext(ctx).Delete(&internal.Page{}, id)
	if result.Error != nil {
		r.logger.Error("Không thể xóa trang", logger.String("error", result.Error.Error()))
		tracing.RecordError(ctx, result.Error)
		return internal.ErrDatabaseError
	}

	if result.RowsAffected == 0 {
		return internal.ErrPageNotFound
	}

	return nil
}

// ListPages lấy danh sách trang với phân trang và lọc
func (r *mysqlRepository) ListPages(ctx context.Context, params dto.ListPagesParams) ([]internal.Page, int64, error) {
	ctx, span := tracing.DatabaseMiddleware(ctx, "SELECT", "website_pages")
	defer span.End()

	query := r.db.WithContext(ctx).Model(&internal.Page{})

	// Áp dụng các điều kiện lọc
	if params.PublishedOnly {
		query = query.Where("published = ?", true)
	}

	if params.Search != "" {
		searchTerm := "%" + params.Search + "%"
		query = query.Where("title LIKE ? OR content LIKE ? OR description LIKE ?", searchTerm, searchTerm, searchTerm)
	}

	// Đếm tổng số bản ghi
	var total int64
	if err := query.Count(&total).Error; err != nil {
		r.logger.Error("Không thể đếm tổng số trang", logger.String("error", err.Error()))
		tracing.RecordError(ctx, err)
		return nil, 0, internal.ErrDatabaseError
	}

	// Xác định thứ tự sắp xếp
	sortBy := params.SortBy
	if sortBy == "" {
		sortBy = "created_at"
	}

	sortOrder := params.SortOrder
	if sortOrder == "" {
		sortOrder = "desc"
	}

	// Áp dụng phân trang và sắp xếp
	page := params.Page
	if page <= 0 {
		page = 1
	}

	pageSize := params.PageSize
	if pageSize <= 0 {
		pageSize = 10
	}

	offset := (page - 1) * pageSize
	query = query.Order(clause.OrderByColumn{Column: clause.Column{Name: sortBy}, Desc: strings.ToLower(sortOrder) == "desc"})
	query = query.Offset(offset).Limit(pageSize)

	// Lấy dữ liệu
	var pages []internal.Page
	if err := query.Find(&pages).Error; err != nil {
		r.logger.Error("Không thể lấy danh sách trang", logger.String("error", err.Error()))
		tracing.RecordError(ctx, err)
		return nil, 0, internal.ErrDatabaseError
	}

	return pages, total, nil
}

// Menu methods
// ---------------------------------

// CreateMenu tạo menu mới
func (r *mysqlRepository) CreateMenu(ctx context.Context, menu *internal.Menu) error {
	ctx, span := tracing.DatabaseMiddleware(ctx, "INSERT", "website_menus")
	defer span.End()

	result := r.db.WithContext(ctx).Create(menu)
	if result.Error != nil {
		r.logger.Error("Không thể tạo menu", logger.String("error", result.Error.Error()))
		tracing.RecordError(ctx, result.Error)
		return internal.ErrDatabaseError
	}

	return nil
}

// GetMenuByID lấy menu theo ID
func (r *mysqlRepository) GetMenuByID(ctx context.Context, id int64) (*internal.Menu, error) {
	ctx, span := tracing.DatabaseMiddleware(ctx, "SELECT", "website_menus")
	defer span.End()

	var menu internal.Menu
	result := r.db.WithContext(ctx).First(&menu, id)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return nil, internal.ErrMenuNotFound
		}
		r.logger.Error("Không thể lấy menu", logger.String("error", result.Error.Error()))
		tracing.RecordError(ctx, result.Error)
		return nil, internal.ErrDatabaseError
	}

	// Lấy các menu items
	items, err := r.GetMenuItemsByMenuID(ctx, menu.ID)
	if err != nil {
		return nil, err
	}
	menu.Items = items

	return &menu, nil
}

// GetMenuByPosition lấy menu theo vị trí
func (r *mysqlRepository) GetMenuByPosition(ctx context.Context, position string) (*internal.Menu, error) {
	ctx, span := tracing.DatabaseMiddleware(ctx, "SELECT", "website_menus")
	defer span.End()

	var menu internal.Menu
	result := r.db.WithContext(ctx).Where("position = ? AND is_active = ?", position, true).First(&menu)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return nil, internal.ErrMenuNotFound
		}
		r.logger.Error("Không thể lấy menu theo vị trí", logger.String("error", result.Error.Error()))
		tracing.RecordError(ctx, result.Error)
		return nil, internal.ErrDatabaseError
	}

	// Lấy các menu items
	items, err := r.GetMenuItemsByMenuID(ctx, menu.ID)
	if err != nil {
		return nil, err
	}
	menu.Items = items

	return &menu, nil
}

// UpdateMenu cập nhật menu
func (r *mysqlRepository) UpdateMenu(ctx context.Context, menu *internal.Menu) error {
	ctx, span := tracing.DatabaseMiddleware(ctx, "UPDATE", "website_menus")
	defer span.End()

	result := r.db.WithContext(ctx).Save(menu)
	if result.Error != nil {
		r.logger.Error("Không thể cập nhật menu", logger.String("error", result.Error.Error()))
		tracing.RecordError(ctx, result.Error)
		return internal.ErrDatabaseError
	}

	if result.RowsAffected == 0 {
		return internal.ErrMenuNotFound
	}

	return nil
}

// DeleteMenu xóa menu
func (r *mysqlRepository) DeleteMenu(ctx context.Context, id int64) error {
	ctx, span := tracing.DatabaseMiddleware(ctx, "DELETE", "website_menus")
	defer span.End()

	// Sử dụng transaction để đảm bảo xóa cả menu items
	tx := r.db.WithContext(ctx).Begin()
	if tx.Error != nil {
		r.logger.Error("Không thể bắt đầu transaction", logger.String("error", tx.Error.Error()))
		tracing.RecordError(ctx, tx.Error)
		return internal.ErrDatabaseError
	}

	// Xóa menu items trước
	if err := tx.Where("menu_id = ?", id).Delete(&internal.MenuItem{}).Error; err != nil {
		tx.Rollback()
		r.logger.Error("Không thể xóa menu items", logger.String("error", err.Error()))
		tracing.RecordError(ctx, err)
		return internal.ErrDatabaseError
	}

	// Xóa menu
	result := tx.Delete(&internal.Menu{}, id)
	if result.Error != nil {
		tx.Rollback()
		r.logger.Error("Không thể xóa menu", logger.String("error", result.Error.Error()))
		tracing.RecordError(ctx, result.Error)
		return internal.ErrDatabaseError
	}

	if result.RowsAffected == 0 {
		tx.Rollback()
		return internal.ErrMenuNotFound
	}

	// Commit transaction
	if err := tx.Commit().Error; err != nil {
		r.logger.Error("Không thể commit transaction", logger.String("error", err.Error()))
		tracing.RecordError(ctx, err)
		return internal.ErrDatabaseError
	}

	return nil
}

// ListMenus lấy danh sách menu với phân trang và lọc
func (r *mysqlRepository) ListMenus(ctx context.Context, params dto.ListMenusParams) ([]internal.Menu, int64, error) {
	ctx, span := tracing.DatabaseMiddleware(ctx, "SELECT", "website_menus")
	defer span.End()

	query := r.db.WithContext(ctx).Model(&internal.Menu{})

	// Áp dụng các điều kiện lọc
	if params.ActiveOnly {
		query = query.Where("is_active = ?", true)
	}

	if params.Position != "" {
		query = query.Where("position = ?", params.Position)
	}

	// Đếm tổng số bản ghi
	var total int64
	if err := query.Count(&total).Error; err != nil {
		r.logger.Error("Không thể đếm tổng số menu", logger.String("error", err.Error()))
		tracing.RecordError(ctx, err)
		return nil, 0, internal.ErrDatabaseError
	}

	// Xác định thứ tự sắp xếp
	sortBy := params.SortBy
	if sortBy == "" {
		sortBy = "created_at"
	}

	sortOrder := params.SortOrder
	if sortOrder == "" {
		sortOrder = "desc"
	}

	// Áp dụng phân trang và sắp xếp
	page := params.Page
	if page <= 0 {
		page = 1
	}

	pageSize := params.PageSize
	if pageSize <= 0 {
		pageSize = 10
	}

	offset := (page - 1) * pageSize
	query = query.Order(clause.OrderByColumn{Column: clause.Column{Name: sortBy}, Desc: strings.ToLower(sortOrder) == "desc"})
	query = query.Offset(offset).Limit(pageSize)

	// Lấy dữ liệu
	var menus []internal.Menu
	if err := query.Find(&menus).Error; err != nil {
		r.logger.Error("Không thể lấy danh sách menu", logger.String("error", err.Error()))
		tracing.RecordError(ctx, err)
		return nil, 0, internal.ErrDatabaseError
	}

	// Lấy các menu items cho mỗi menu
	for i := range menus {
		items, err := r.GetMenuItemsByMenuID(ctx, menus[i].ID)
		if err != nil {
			return nil, 0, err
		}
		menus[i].Items = items
	}

	return menus, total, nil
}

// Menu item methods
// ---------------------------------

// CreateMenuItem tạo mục menu mới
func (r *mysqlRepository) CreateMenuItem(ctx context.Context, item *internal.MenuItem) error {
	ctx, span := tracing.DatabaseMiddleware(ctx, "INSERT", "website_menu_items")
	defer span.End()

	result := r.db.WithContext(ctx).Create(item)
	if result.Error != nil {
		r.logger.Error("Không thể tạo mục menu", logger.String("error", result.Error.Error()))
		tracing.RecordError(ctx, result.Error)
		return internal.ErrDatabaseError
	}

	return nil
}

// GetMenuItemsByMenuID lấy các mục menu theo menu ID
func (r *mysqlRepository) GetMenuItemsByMenuID(ctx context.Context, menuID int64) ([]internal.MenuItem, error) {
	ctx, span := tracing.DatabaseMiddleware(ctx, "SELECT", "website_menu_items")
	defer span.End()

	var items []internal.MenuItem
	result := r.db.WithContext(ctx).
		Where("menu_id = ?", menuID).
		Order("parent_id, `order`, id").
		Find(&items)

	if result.Error != nil {
		r.logger.Error("Không thể lấy các mục menu", logger.String("error", result.Error.Error()))
		tracing.RecordError(ctx, result.Error)
		return nil, internal.ErrDatabaseError
	}

	return items, nil
}

// UpdateMenuItem cập nhật mục menu
func (r *mysqlRepository) UpdateMenuItem(ctx context.Context, item *internal.MenuItem) error {
	ctx, span := tracing.DatabaseMiddleware(ctx, "UPDATE", "website_menu_items")
	defer span.End()

	result := r.db.WithContext(ctx).Save(item)
	if result.Error != nil {
		r.logger.Error("Không thể cập nhật mục menu", logger.String("error", result.Error.Error()))
		tracing.RecordError(ctx, result.Error)
		return internal.ErrDatabaseError
	}

	if result.RowsAffected == 0 {
		return internal.ErrMenuNotFound
	}

	return nil
}

// DeleteMenuItem xóa mục menu
func (r *mysqlRepository) DeleteMenuItem(ctx context.Context, id int64) error {
	ctx, span := tracing.DatabaseMiddleware(ctx, "DELETE", "website_menu_items")
	defer span.End()

	// Kiểm tra xem có mục con không
	var count int64
	if err := r.db.WithContext(ctx).Model(&internal.MenuItem{}).Where("parent_id = ?", id).Count(&count).Error; err != nil {
		r.logger.Error("Không thể kiểm tra mục con", logger.String("error", err.Error()))
		tracing.RecordError(ctx, err)
		return internal.ErrDatabaseError
	}

	// Nếu có mục con, cập nhật parent_id của chúng thành NULL
	if count > 0 {
		if err := r.db.WithContext(ctx).Model(&internal.MenuItem{}).Where("parent_id = ?", id).Update("parent_id", nil).Error; err != nil {
			r.logger.Error("Không thể cập nhật mục con", logger.String("error", err.Error()))
			tracing.RecordError(ctx, err)
			return internal.ErrDatabaseError
		}
	}

	// Xóa mục menu
	result := r.db.WithContext(ctx).Delete(&internal.MenuItem{}, id)
	if result.Error != nil {
		r.logger.Error("Không thể xóa mục menu", logger.String("error", result.Error.Error()))
		tracing.RecordError(ctx, result.Error)
		return internal.ErrDatabaseError
	}

	if result.RowsAffected == 0 {
		return internal.ErrMenuNotFound
	}

	return nil
}

// Banner methods
// ---------------------------------

// CreateBanner tạo banner mới
func (r *mysqlRepository) CreateBanner(ctx context.Context, banner *internal.Banner) error {
	ctx, span := tracing.DatabaseMiddleware(ctx, "INSERT", "website_banners")
	defer span.End()

	result := r.db.WithContext(ctx).Create(banner)
	if result.Error != nil {
		r.logger.Error("Không thể tạo banner", logger.String("error", result.Error.Error()))
		tracing.RecordError(ctx, result.Error)
		return internal.ErrDatabaseError
	}

	return nil
}

// GetBannerByID lấy banner theo ID
func (r *mysqlRepository) GetBannerByID(ctx context.Context, id int64) (*internal.Banner, error) {
	ctx, span := tracing.DatabaseMiddleware(ctx, "SELECT", "website_banners")
	defer span.End()

	var banner internal.Banner
	result := r.db.WithContext(ctx).First(&banner, id)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return nil, internal.ErrBannerNotFound
		}
		r.logger.Error("Không thể lấy banner", logger.String("error", result.Error.Error()))
		tracing.RecordError(ctx, result.Error)
		return nil, internal.ErrDatabaseError
	}

	return &banner, nil
}

// UpdateBanner cập nhật banner
func (r *mysqlRepository) UpdateBanner(ctx context.Context, banner *internal.Banner) error {
	ctx, span := tracing.DatabaseMiddleware(ctx, "UPDATE", "website_banners")
	defer span.End()

	result := r.db.WithContext(ctx).Save(banner)
	if result.Error != nil {
		r.logger.Error("Không thể cập nhật banner", logger.String("error", result.Error.Error()))
		tracing.RecordError(ctx, result.Error)
		return internal.ErrDatabaseError
	}

	if result.RowsAffected == 0 {
		return internal.ErrBannerNotFound
	}

	return nil
}

// DeleteBanner xóa banner
func (r *mysqlRepository) DeleteBanner(ctx context.Context, id int64) error {
	ctx, span := tracing.DatabaseMiddleware(ctx, "DELETE", "website_banners")
	defer span.End()

	result := r.db.WithContext(ctx).Delete(&internal.Banner{}, id)
	if result.Error != nil {
		r.logger.Error("Không thể xóa banner", logger.String("error", result.Error.Error()))
		tracing.RecordError(ctx, result.Error)
		return internal.ErrDatabaseError
	}

	if result.RowsAffected == 0 {
		return internal.ErrBannerNotFound
	}

	return nil
}

// ListBanners lấy danh sách banner với phân trang và lọc
func (r *mysqlRepository) ListBanners(ctx context.Context, params dto.ListBannersParams) ([]internal.Banner, int64, error) {
	ctx, span := tracing.DatabaseMiddleware(ctx, "SELECT", "website_banners")
	defer span.End()

	query := r.db.WithContext(ctx).Model(&internal.Banner{})

	// Áp dụng các điều kiện lọc
	if params.ActiveOnly {
		query = query.Where("is_active = ?", true)
	}

	if params.Position != "" {
		query = query.Where("position = ?", params.Position)
	}

	// Đếm tổng số bản ghi
	var total int64
	if err := query.Count(&total).Error; err != nil {
		r.logger.Error("Không thể đếm tổng số banner", logger.String("error", err.Error()))
		tracing.RecordError(ctx, err)
		return nil, 0, internal.ErrDatabaseError
	}

	// Xác định thứ tự sắp xếp
	sortBy := params.SortBy
	if sortBy == "" {
		sortBy = "order"
	}

	sortOrder := params.SortOrder
	if sortOrder == "" {
		sortOrder = "asc"
	}

	// Áp dụng phân trang và sắp xếp
	page := params.Page
	if page <= 0 {
		page = 1
	}

	pageSize := params.PageSize
	if pageSize <= 0 {
		pageSize = 10
	}

	offset := (page - 1) * pageSize
	query = query.Order(clause.OrderByColumn{Column: clause.Column{Name: sortBy}, Desc: strings.ToLower(sortOrder) == "desc"})
	query = query.Offset(offset).Limit(pageSize)

	// Lấy dữ liệu
	var banners []internal.Banner
	if err := query.Find(&banners).Error; err != nil {
		r.logger.Error("Không thể lấy danh sách banner", logger.String("error", err.Error()))
		tracing.RecordError(ctx, err)
		return nil, 0, internal.ErrDatabaseError
	}

	return banners, total, nil
}

// GetActiveBannersByPosition lấy các banner đang hoạt động theo vị trí
func (r *mysqlRepository) GetActiveBannersByPosition(ctx context.Context, position string) ([]internal.Banner, error) {
	ctx, span := tracing.DatabaseMiddleware(ctx, "SELECT", "website_banners")
	defer span.End()

	now := time.Now()

	var banners []internal.Banner
	result := r.db.WithContext(ctx).
		Where("position = ? AND is_active = ?", position, true).
		Where("(start_date IS NULL OR start_date <= ?)", now).
		Where("(end_date IS NULL OR end_date >= ?)", now).
		Order("`order` ASC").
		Find(&banners)

	if result.Error != nil {
		r.logger.Error("Không thể lấy banner theo vị trí", logger.String("error", result.Error.Error()))
		tracing.RecordError(ctx, result.Error)
		return nil, internal.ErrDatabaseError
	}

	return banners, nil
}
