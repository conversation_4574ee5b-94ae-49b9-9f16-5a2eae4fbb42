package models

import (
	"database/sql/driver"
	"encoding/json"
	"errors"
	"time"
)

// Value chuyển đổi Content sang định dạng lưu trong DB
func (c Content) Value() (driver.Value, error) {
	return json.Marshal(c)
}

// Scan chuyển đổi dữ liệu từ DB sang Content
func (c *Content) Scan(value interface{}) error {
	if value == nil {
		return nil
	}

	b, ok := value.([]byte)
	if !ok {
		return errors.New("type assertion to []byte failed")
	}

	return json.Unmarshal(b, &c)
}

// Page mô tả một trang trong hệ thống
type Page struct {
	PageID          int        `db:"page_id" json:"page_id" gorm:"primaryKey"`
	TenantID        int        `db:"tenant_id" json:"tenant_id"`
	WebsiteID       int        `db:"website_id" json:"website_id"`
	Title           string     `db:"title" json:"title"`
	Slug            string     `db:"slug" json:"slug"`
	Content         Content    `db:"content" json:"content,omitempty"`
	Layout          *string    `db:"layout" json:"layout,omitempty"`
	MetaTitle       *string    `db:"meta_title" json:"meta_title,omitempty"`
	MetaDescription *string    `db:"meta_description" json:"meta_description,omitempty"`
	IsHomepage      bool       `db:"is_homepage" json:"is_homepage"`
	Status          string     `db:"status" json:"status"` // draft, published, archived
	PublishedAt     *time.Time `db:"published_at" json:"published_at,omitempty"`
	CreatedAt       time.Time  `db:"created_at" json:"created_at"`
	UpdatedAt       time.Time  `db:"updated_at" json:"updated_at"`

	// Virtual fields (không lưu trong DB)
	Website *Website `db:"-" json:"website,omitempty"`
}
