package service

import (
	"context"
	"fmt"

	"wnapi/internal/pkg/constants"
	"wnapi/modules/notification/dto"
	"wnapi/modules/notification/internal"
	"wnapi/modules/notification/models"
	"wnapi/modules/notification/repository"
)

// NotificationUserService implements NotificationUserService interface
type NotificationUserService struct {
	repo repository.NotificationUserRepository
}

// NewNotificationUserService creates a new notification user service
func NewNotificationUserService(repo repository.NotificationUserRepository) internal.NotificationUserService {
	return &NotificationUserService{
		repo: repo,
	}
}

// CreateNotificationUser creates a new notification for a user
func (s *NotificationUserService) CreateNotificationUser(ctx context.Context, req *dto.CreateNotificationUserRequest) (*models.NotificationUser, error) {
	notification := &models.NotificationUser{
		TenantID: req.TenantID,
		UserID:   req.UserID,
		Title:    req.Title,
		Content:  req.Content,
		IsRead:   false, // Default to unread
	}

	notificationID, err := s.repo.Create(ctx, notification)
	if err != nil {
		return nil, fmt.Errorf("failed to create notification user: %w", err)
	}

	return s.GetNotificationUserByID(ctx, notificationID)
}

// GetNotificationUserByID gets a notification by ID
func (s *NotificationUserService) GetNotificationUserByID(ctx context.Context, notificationID uint) (*models.NotificationUser, error) {
	notification, err := s.repo.GetByID(ctx, notificationID)
	if err != nil {
		return nil, fmt.Errorf("failed to get notification user: %w", err)
	}

	if notification == nil {
		return nil, fmt.Errorf("notification not found")
	}

	return notification, nil
}

// GetUserNotifications gets notifications for a user with pagination
func (s *NotificationUserService) GetUserNotifications(
	ctx context.Context,
	tenantID *uint,
	isRead *bool,
	cursor string,
	limit int,
	userID *uint,
) ([]*models.NotificationUser, string, error) {
	if limit <= 0 {
		limit = 20 // Default limit
	}

	// Use provided userID if available, otherwise get from context
	var userIDValue uint
	if userID != nil {
		userIDValue = *userID
	} else {
		// Get userID from context
		ctxUserID := ctx.Value(constants.UserIDContextKey)
		if ctxUserID == nil {
			return nil, "", fmt.Errorf("user ID not found in context")
		}

		var ok bool
		userIDValue, ok = ctxUserID.(uint)
		if !ok {
			return nil, "", fmt.Errorf("invalid user ID in context")
		}
	}

	filters := make(map[string]interface{})
	if tenantID != nil {
		filters["tenant_id"] = *tenantID
	}
	if isRead != nil {
		filters["is_read"] = *isRead
	}

	notifications, nextCursor, err := s.repo.GetByUserID(ctx, userIDValue, cursor, limit, filters)
	if err != nil {
		return nil, "", fmt.Errorf("failed to get user notifications: %w", err)
	}

	return notifications, nextCursor, nil
}

// MarkAsRead marks a notification as read
func (s *NotificationUserService) MarkAsRead(ctx context.Context, notificationID uint) error {
	err := s.repo.MarkAsRead(ctx, notificationID)
	if err != nil {
		return fmt.Errorf("failed to mark notification as read: %w", err)
	}

	return nil
}

// MarkAllAsRead marks all notifications for a user as read
func (s *NotificationUserService) MarkAllAsRead(ctx context.Context, userID uint, tenantID *uint) error {
	err := s.repo.MarkAllAsRead(ctx, userID, tenantID)
	if err != nil {
		return fmt.Errorf("failed to mark all notifications as read: %w", err)
	}

	return nil
}

// GetUnreadCount gets the count of unread notifications for a user
func (s *NotificationUserService) GetUnreadCount(ctx context.Context, userID uint, tenantID *uint) (int, error) {
	count, err := s.repo.GetUnreadCount(ctx, userID, tenantID)
	if err != nil {
		return 0, fmt.Errorf("failed to get unread notification count: %w", err)
	}

	return count, nil
}

// UpdateNotificationUser updates a notification
func (s *NotificationUserService) UpdateNotificationUser(
	ctx context.Context,
	notificationID uint,
	req *dto.UpdateNotificationUserRequest,
) (*models.NotificationUser, error) {
	notification, err := s.GetNotificationUserByID(ctx, notificationID)
	if err != nil {
		return nil, err
	}

	// Update fields if provided
	if req.Title != "" {
		notification.Title = req.Title
	}
	if req.Content != "" {
		notification.Content = req.Content
	}
	if req.IsRead != nil {
		notification.IsRead = *req.IsRead
	}

	err = s.repo.Update(ctx, notification)
	if err != nil {
		return nil, fmt.Errorf("failed to update notification: %w", err)
	}

	return notification, nil
}

// DeleteNotificationUser deletes a notification
func (s *NotificationUserService) DeleteNotificationUser(ctx context.Context, notificationID uint) error {
	err := s.repo.Delete(ctx, notificationID)
	if err != nil {
		return fmt.Errorf("failed to delete notification: %w", err)
	}

	return nil
}
