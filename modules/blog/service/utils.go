package service

import (
	"context"
	"wnapi/internal/pkg/constants"
)

// getUserIDFromContext extracts the user ID from context
func getUserIDFromContext(ctx context.Context) uint {
	// Try to get user ID from context - using the correct key
	userIDValue := ctx.Value(constants.UserIDContextKey)
	if userIDValue != nil {
		// Convert to uint if possible
		if userID, ok := userIDValue.(uint); ok {
			return userID
		}
	}

	// For testing purposes, return a default user ID
	return 1
}

// Các hàm tiện ích khác có thể được thêm vào đây trong tương lai
