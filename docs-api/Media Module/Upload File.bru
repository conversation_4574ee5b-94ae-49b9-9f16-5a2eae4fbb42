meta {
  name: Upload File
  type: http
  seq: 2
}

post {
  url: {{base_url}}/api/admin/v1/media/upload
  body: multipartForm
  auth: none
}

headers {
  Authorization: Bearer {{access_token}}
  Content-Type: application/json
}

body:multipart-form {
  file: @file(sample.jpg)
  is_public: true
  folder_id: 
}

script:post-response {
  // Parse response
  const response = res;
  const responseJson = response.body;
  
  // Check if response is successful
  if (responseJson.status && responseJson.status.success === true && responseJson.data) {
    // Extract media details
    const mediaId = responseJson.data.id;
    const filename = responseJson.data.filename;
    const originalFilename = responseJson.data.original_filename;
    const publicUrl = responseJson.data.public_url;
    const mediaType = responseJson.data.media_type;
    const contentType = responseJson.data.content_type;
    const size = responseJson.data.size;
    const status = responseJson.data.status;
    
    // Save to environment variables
    bru.setEnvVar("media_id", mediaId);
    bru.setEnvVar("media_filename", filename);
    bru.setEnvVar("media_original_filename", originalFilename);
    bru.setEnvVar("media_public_url", publicUrl);
    bru.setEnvVar("media_type", mediaType);
    bru.setEnvVar("media_content_type", contentType);
    bru.setEnvVar("media_size", size);
    bru.setEnvVar("media_status", status);
    
    console.log("Media upload data saved:");
    console.log(`Media ID: ${mediaId}`);
    console.log(`Filename: ${filename}`);
    console.log(`Original Filename: ${originalFilename}`);
    console.log(`Public URL: ${publicUrl}`);
    console.log(`Media Type: ${mediaType}`);
    console.log(`Content Type: ${contentType}`);
    console.log(`Size: ${size} bytes`);
    console.log(`Status: ${status}`);
  }
}

docs {
  title: "Upload Media File"
  desc: "Upload a media file (image, video, audio, document) to the server. The file will be processed and stored based on the configured storage type (local or S3)."
}
