meta {
  name: Dang <PERSON>p
  type: http
  seq: 2
}

post {
  url: {{api_url}}/api/admin/v1/auth/login
  body: json
  auth: inherit
}

body:json {
  {
    "email": "<EMAIL>",
    "password": "12345678"
  }
}

script:post-response {
  // Parse response
    const response = res;
    const responseJson = response.body;
    
    // Check if response is successful
    if (responseJson.status && responseJson.status.success === true && responseJson.data) {
      // Extract tokens
      const accessToken = responseJson.data.access_token;
      const refreshToken = responseJson.data.refresh_token;
      const accessTokenExpiresIn = responseJson.data.access_token_expires_in;
      const refreshTokenExpiresIn = responseJson.data.refresh_token_expires_in;
      const tokenType = responseJson.data.token_type;
    
      // Calculate expiration timestamps
      const now = Math.floor(Date.now() / 1000);
      const accessTokenExpiresAt = now + accessTokenExpiresIn;
      const refreshTokenExpiresAt = now + refreshTokenExpiresIn;
    
      // Save to environment variables
      bru.setEnvVar("access_token", accessToken);
      bru.setEnvVar("bearerToken", accessToken);
      bru.setEnvVar("refresh_token", refreshToken);
      bru.setEnvVar("token_type", tokenType);
      bru.setEnvVar("access_token_expires_at", accessTokenExpiresAt);
      bru.setEnvVar("refresh_token_expires_at", refreshTokenExpiresAt);
    
      // Log expiration dates
      const accessTokenExpiryDate = new Date(accessTokenExpiresAt * 1000).toLocaleString();
      const refreshTokenExpiryDate = new Date(refreshTokenExpiresAt * 1000).toLocaleString();
      
      console.log("Auth credentials saved:");
      console.log(`Access Token (expires: ${accessTokenExpiryDate})`);
      console.log(`Refresh Token (expires: ${refreshTokenExpiryDate})`);
    }
}

docs {
  Đây là docs
}
